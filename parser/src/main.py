#!/usr/bin/env python3
"""
表格解析系统主入口

重构后的统一入口点，替换原有的parse_pipeline.py
"""
import os
import sys
import logging
import argparse
from pathlib import Path
import colorlog
from typing import Dict, List, Optional
import time
import requests

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from core.pipeline import ParsePipeline
from tasks.data_generator import DataGenerator
from tasks.generator_wrapper import GeneratorEngineWrapper

from services.auto_annotation import AutoAnnotationService


def setup_logging():
    """设置日志配置"""
    # 从环境变量读取配置
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    log_file = os.getenv('LOG_FILE', 'parse.log')
    use_color = os.getenv('LOG_USE_COLOR', 'true').lower() == 'true'

    # 转换日志级别字符串到logging常量
    level = getattr(logging, log_level, logging.INFO)
    
    # 创建日志格式
    if use_color:
        # 彩色日志格式
        colors = {
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
        formatter = colorlog.ColoredFormatter(
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            log_colors=colors,
            reset=True
        )
    else:
        # 普通日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    # 配置控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)

    # 配置文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))

    # 获取根日志记录器并清除现有处理器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    # 设置日志级别和添加处理器
    root_logger.setLevel(level)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)


def run_parse_only():
    """只运行解析流程"""
    print("🔍 执行解析流程...")
    pipeline = ParsePipeline()
    result_file = pipeline.run_full_pipeline()
    print(f"✅ 解析完成，结果文件: {result_file}")
    return result_file


def run_report_only():
    """只生成报告"""
    print("📊 生成报告...")
    print("✅ 报告生成完成")


def run_progress_report():
    """生成当前进度报告"""
    print("📈 生成进度报告...")
    print("✅ 进度报告生成完成")


def run_auto_annotation(dataset_name=None):
    """运行自动标注"""
    if dataset_name is None:
        dataset_name = os.getenv("DATASET_NAME", "default_test")

    print("🏷️  开始自动标注...")

    # 获取必要的目录路径
    images_dir = os.getenv("IMAGES_DIR")
    backend_url = os.getenv("BACKEND_URL", "http://localhost:8000")

    if not images_dir or not os.path.exists(images_dir):
        print(f"在目录 {images_dir} 中未找到图片文件")
        return False

    try:
        # 直接调用backend API进行自动标注
        import requests
        import json
        
        url = f"{backend_url}/api/annotations/auto-generate/{dataset_name}"
        params = {
            'annotator': 'auto_generator',
            'overwrite': True
        }
        
        print(f"📡 调用自动标注API: {url}")
        
        # 首先调用datasets接口触发数据集扫描
        print(f"🔍 触发数据集扫描...")
        datasets_url = f"{backend_url}/api/datasets"
        try:
            datasets_response = requests.get(datasets_url, timeout=10)
            if datasets_response.status_code == 200:
                print(f"✅ 数据集扫描完成")
            else:
                print(f"⚠️  数据集扫描返回状态码: {datasets_response.status_code}")
        except Exception as scan_e:
            print(f"⚠️  数据集扫描失败: {scan_e}")
        
        # 重试机制：最多重试4次，每次等待8秒（backend需要时间处理上传的文件）
        max_retries = 4
        for attempt in range(max_retries):
            try:
                response = requests.post(url, params=params, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    stats = result.get('stats', {})
                    
                    success_count = stats.get('success_count', 0)
                    failed_count = stats.get('failed_count', 0)
                    skipped_count = stats.get('skipped_count', 0)
                    errors = stats.get('errors', [])
                    
                    print(f"✅ 自动标注完成: 成功 {success_count} 个，失败 {failed_count} 个，跳过 {skipped_count} 个")
                    
                    if errors:
                        print("❌ 错误详情:")
                        for error in errors[:5]:  # 只显示前5个错误
                            print(f"   - {error}")
                            
                    return success_count > 0
                elif response.status_code == 404:
                    if attempt < max_retries - 1:
                        print(f"⏳ 数据集尚未在数据库中创建，等待8秒后重试... (尝试 {attempt + 1}/{max_retries})")
                        time.sleep(8)
                        continue
                    else:
                        print(f"❌ 数据集在数据库中不存在: {dataset_name}")
                        return False
                else:
                    print(f"❌ 自动标注API调用失败: {response.status_code} - {response.text}")
                    return False
            except requests.exceptions.RequestException as req_e:
                if attempt < max_retries - 1:
                    print(f"⏳ 网络请求失败，等待8秒后重试... (尝试 {attempt + 1}/{max_retries}): {req_e}")
                    time.sleep(8)
                    continue
                else:
                    raise req_e
        
        return False

    except Exception as e:
        print(f"❌ 自动标注失败: {e}")
        return False


def run_full_workflow(generate_args=None):
    """运行完整工作流程"""
    print("🚀 运行完整工作流程...")

    # 1. 生成数据（如果需要）
    dataset_name = os.getenv("DATASET_NAME", "default_test")
    images_dir = os.getenv("IMAGES_DIR")
    data_generated = False

    if images_dir and os.path.exists(images_dir):
        image_files = [f for f in os.listdir(images_dir)
                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]
        if not image_files:
            print("📝 图片目录为空，开始生成数据...")
            
            # 根据generator_engine选择生成器
            engine = generate_args.get('generator_engine', 'generator') if generate_args else 'generator'
            
            if engine == 'generator':
                print("🔧 使用 Generator 引擎生成数据...")
                generator = GeneratorEngineWrapper()
                if generate_args:
                    generator.generate_tables(
                        num_tables=generate_args.get('num_tables', 2),
                        use_llm=generate_args.get('use_llm', True),
                        topics=generate_args.get('topics'),
                        strict_llm=generate_args.get('strict_llm', False)
                    )
                else:
                    generator.generate_tables(num_tables=2, use_llm=True)
            else:  # tasks
                print("🔧 使用 Tasks 引擎生成数据...")
                data_generator = DataGenerator()
                if generate_args:
                    data_generator.generate_tables(
                        num_tables=generate_args.get('num_tables', 2),
                        use_llm=generate_args.get('use_llm', True),
                        topics=generate_args.get('topics'),
                        strict_llm=generate_args.get('strict_llm', False)
                    )
                else:
                    data_generator.generate_tables(num_tables=2, use_llm=True)
            
            print("✅ 数据生成完成")
            data_generated = True
        else:
            print(f"📁 发现 {len(image_files)} 个图片文件，跳过数据生成")
    else:
        print("📝 图片目录不存在，开始生成数据...")
        
        # 根据generator_engine选择生成器
        engine = generate_args.get('generator_engine', 'generator') if generate_args else 'generator'
        
        if engine == 'generator':
            print("🔧 使用 Generator 引擎生成数据...")
            generator = GeneratorEngineWrapper()
            if generate_args:
                generator.generate_tables(
                    num_tables=generate_args.get('num_tables', 2),
                    use_llm=generate_args.get('use_llm', True),
                    topics=generate_args.get('topics'),
                    strict_llm=generate_args.get('strict_llm', False)
                )
            else:
                generator.generate_tables(num_tables=2, use_llm=True)
        else:  # tasks
            print("🔧 使用 Tasks 引擎生成数据...")
            data_generator = DataGenerator()
            if generate_args:
                data_generator.generate_tables(
                    num_tables=generate_args.get('num_tables', 2),
                    use_llm=generate_args.get('use_llm', True),
                    topics=generate_args.get('topics'),
                    strict_llm=generate_args.get('strict_llm', False)
                )
            else:
                data_generator.generate_tables(num_tables=2, use_llm=True)
        
        print("✅ 数据生成完成")
        data_generated = True

    # 2. 执行解析（这里会触发backend扫描数据集）
    result_file = run_parse_only()

    # 3. 自动标注（在解析完成后执行，确保数据集已在backend数据库中）
    run_auto_annotation(dataset_name)

    # 4. 生成报告
    run_report_only()

    print("🎉 完整工作流程执行完成！")
    return result_file


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='表格解析系统')
    parser.add_argument('--mode', choices=['parse', 'report', 'progress', 'full', 'annotate'],
                       default='full', help='运行模式')
    parser.add_argument('--generate-data', action='store_true',
                       help='强制重新生成数据')
    parser.add_argument('--num-tables', type=int, default=2,
                       help='生成表格数量')
    parser.add_argument('--use-llm', action='store_true', default=True,
                       help='使用LLM生成表格')
    parser.add_argument('--topics', nargs='*',
                       help='指定表格主题')
    parser.add_argument('--generator-engine', choices=['tasks', 'generator'],
                       default='generator', help='生成器引擎选择')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式')

    args = parser.parse_args()

    # 设置日志级别
    setup_logging()

    logger = logging.getLogger(__name__)
    logger.info(f"启动表格解析系统，模式: {args.mode}")
    logger.info(f"当前测试集: {os.getenv('DATASET_NAME', 'default_test')}")

    try:
        if args.mode == 'parse':
            run_parse_only()
        elif args.mode == 'report':
            run_report_only()
        elif args.mode == 'progress':
            run_progress_report()
        elif args.mode == 'annotate':
            run_auto_annotation()
        elif args.mode == 'full':
            generate_args = None
            if args.generate_data or args.topics:
                generate_args = {
                    'num_tables': args.num_tables,
                    'use_llm': args.use_llm,
                    'topics': args.topics,
                    'generator_engine': args.generator_engine,
                    'strict_llm': args.use_llm  # 当使用--use-llm时启用严格模式
                }
            run_full_workflow(generate_args)

        logger.info("程序执行完成")

    except KeyboardInterrupt:
        logger.info("用户中断程序")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {type(e).__name__}: {str(e)}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 