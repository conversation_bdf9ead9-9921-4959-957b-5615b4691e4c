"""
Generator 引擎包装器
调用 generator/ 目录下的数据生成能力
"""
import os
import sys
import json
import subprocess
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional


class GeneratorEngineWrapper:
    """Generator 引擎包装器"""
    
    def __init__(self):
        # 配置目录
        self.project_root = os.getenv("PROJECT_ROOT_DIR", os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))
        self.generator_dir = os.path.join(self.project_root, "generator")
        self.dataset_name = os.getenv("DATASET_NAME", "kingsoft")
        self.dataset_dir = os.path.join(self.project_root, "dataset", self.dataset_name)
        self.images_dir = os.path.join(self.dataset_dir, "images")
        self.annotations_dir = os.path.join(self.dataset_dir, "annotations")
        
        # 确保目录存在
        os.makedirs(self.images_dir, exist_ok=True)
        os.makedirs(self.annotations_dir, exist_ok=True)
        
    def generate_tables(self, num_tables=10, use_llm=False, topics=None, strict_llm=False):
        """
        使用 generator/ 引擎生成表格数据
        
        Args:
            num_tables: 生成表格数量
            use_llm: 是否使用LLM（generator引擎有自己的LLM配置）
            topics: 指定的主题列表（generator引擎可能不支持）
            strict_llm: 严格LLM模式（generator引擎有自己的机制）
        """
        print(f"🚀 使用 Generator 引擎生成 {num_tables} 个表格...")
        
        try:
            # 切换到generator目录
            original_cwd = os.getcwd()
            os.chdir(self.generator_dir)
            
            # 执行generator的start_gen.sh脚本
            # 设置环境变量，指定输出目录和数量
            env = os.environ.copy()
            env["GENERATOR_OUTPUT_DIR"] = self.dataset_dir
            env["GENERATOR_NUM_TABLES"] = str(num_tables)
            env["DATASET_NAME"] = self.dataset_name
            
            print(f"📁 输出目录: {self.dataset_dir}")
            print(f"🔧 执行generator脚本...")
            
            # 执行脚本，传递num_tables参数
            result = subprocess.run(
                ["bash", "start_gen.sh", "--num-samples", str(num_tables)],
                env=env,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                print("✅ Generator 脚本执行成功")
                print(f"输出:\n{result.stdout}")
                
                # 处理生成的数据
                self._process_generator_output(num_tables)
                
                return True
            else:
                print(f"❌ Generator 脚本执行失败")
                print(f"错误输出:\n{result.stderr}")
                print(f"标准输出:\n{result.stdout}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Generator 脚本执行超时")
            return False
        except Exception as e:
            print(f"❌ 执行 Generator 引擎失败: {e}")
            return False
        finally:
            # 恢复原始工作目录
            os.chdir(original_cwd)
    
    def _process_generator_output(self, num_tables: int):
        """处理generator输出的数据，移动到正确的位置"""
        print("📦 处理 Generator 输出数据...")
        
        # 导入glob模块
        import glob
        
        # 只处理最新的生成目录，避免处理历史数据
        data_dirs = glob.glob(os.path.join(self.generator_dir, "data", "2025-*"))
        if not data_dirs:
            print("⚠️ 未找到generator数据目录")
            return
            
        # 获取最新的目录（按修改时间排序）
        latest_dir = max(data_dirs, key=os.path.getmtime)
        print(f"📂 使用最新的generator数据目录: {os.path.basename(latest_dir)}")
        
        # generator可能输出数据到多个位置，只在最新目录中查找
        generator_output_patterns = [
            os.path.join(latest_dir, "train_data.json"),  # 优先使用train_data.json
            os.path.join(latest_dir, "*.json"),  # 最新目录下的JSON文件
        ]
        
        # 查找生成的JSON文件
        json_files = []
        for pattern in generator_output_patterns:
            json_files.extend(glob.glob(pattern))
        
        if json_files:
            print(f"📄 发现 {len(json_files)} 个JSON文件")
            
            # 将JSON文件复制到annotations目录，限制为指定数量
            max_files = min(num_tables, 20)  # 限制为num_tables或20个文件，取较小值
            for idx, json_file in enumerate(json_files[:max_files]):
                try:
                    # 读取JSON文件
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 检查是否是train_data.json格式（图片训练数据）
                    if os.path.basename(json_file) == "train_data.json" and isinstance(data, list) and len(data) > 0:
                        if 'image_path' in data[0]:
                            # 处理train_data格式
                            self._process_train_data(data, json_file, num_tables)
                            continue
                    
                    # 生成新的文件名（使用JSON内容的hash）
                    import hashlib
                    content_hash = hashlib.md5(json.dumps(data, sort_keys=True).encode()).hexdigest()
                    new_filename = f"{content_hash}.json"
                    new_path = os.path.join(self.annotations_dir, new_filename)
                    
                    # 对于非train_data.json文件，通常是纯文本数据，无需bbox信息
                    # 因为只有HTML渲染的数据才会有bbox信息
                    print(f"📝 处理纯文本JSON数据: {os.path.basename(json_file)}")
                    
                    # 如果JSON数据需要转换格式，在这里处理（bbox_data为None）
                    processed_data = self._convert_generator_json(data, idx+1, None)
                    
                    # 保存到annotations目录
                    with open(new_path, 'w', encoding='utf-8') as f:
                        json.dump(processed_data, f, ensure_ascii=False, indent=2)
                    
                    print(f"✅ 处理文件: {json_file} -> {new_path}")
                    
                except Exception as e:
                    print(f"❌ 处理文件失败 {json_file}: {e}")
        else:
            print("⚠️ 未发现生成的JSON文件")
        
        # 注意：图片文件由_process_train_data方法处理，这里不再重复复制
        print("ℹ️ 图片文件将在处理train_data.json时复制，避免重复")
    
    def _process_train_data(self, train_data: List[Dict], json_file_path: str, max_tables: int = None):
        """处理train_data.json格式的数据"""
        # 如果指定了最大表格数量，则限制处理的数据量
        if max_tables is not None:
            train_data = train_data[:max_tables]
        print(f"📊 处理训练数据文件，包含 {len(train_data)} 个训练样本")
        
        # 获取json_mapping.json来获取原始数据
        json_mapping_path = os.path.join(os.path.dirname(json_file_path), "json_mapping.json")
        json_mapping = {}
        if os.path.exists(json_mapping_path):
            with open(json_mapping_path, 'r', encoding='utf-8') as f:
                json_mapping = json.load(f)
        
        # 根据图片和JSON的对应关系创建annotations
        processed_items = {}
        for item in train_data:
            json_id = item.get('json_id')
            image_path = item.get('image_path')
            bbox_data = item.get('bbox_data', [])  # 获取bbox信息
            
            if json_id is not None and image_path and os.path.exists(image_path):
                # 获取原始JSON数据
                if str(json_id) in json_mapping:
                    original_json = json_mapping[str(json_id)]
                    
                    # 生成唯一的标识符
                    image_basename = os.path.basename(image_path)
                    image_name = os.path.splitext(image_basename)[0]
                    
                    if image_name not in processed_items:
                        # 复制图片到images目录
                        new_image_path = os.path.join(self.images_dir, image_basename)
                        shutil.copy2(image_path, new_image_path)
                        print(f"✅ 复制图片: {image_path} -> {new_image_path}")
                        
                        # 创建annotation文件
                        annotation_filename = f"{image_name}.json"
                        annotation_path = os.path.join(self.annotations_dir, annotation_filename)
                        
                        # 转换为标准格式并添加bbox信息
                        processed_data = self._convert_generator_json(original_json, json_id + 1, bbox_data)
                        
                        # 保存annotation
                        with open(annotation_path, 'w', encoding='utf-8') as f:
                            json.dump(processed_data, f, ensure_ascii=False, indent=2)
                        
                        processed_items[image_name] = True
                        print(f"✅ 创建标注: {annotation_path}")
                        if bbox_data:
                            print(f"✅ 包含 {len(bbox_data)} 个元素的bbox信息")
        
        print(f"✅ 训练数据处理完成，共处理 {len(processed_items)} 个样本")
    
    def _convert_generator_json(self, data: Dict, table_id: int, bbox_data: List = None) -> Dict:
        """
        转换generator输出的JSON格式为标准annotations格式
        
        Args:
            data: generator输出的原始JSON数据
            table_id: 表格ID
            bbox_data: 页面元素的bbox信息列表
            
        Returns:
            Dict: 转换后的标准格式数据
        """
        # 如果数据已经是标准格式，添加bbox信息后返回
        if isinstance(data, dict) and 'elements' in data and isinstance(data['elements'], list):
            result = data.copy()
            if bbox_data:
                result = self._add_bbox_to_elements(result, bbox_data)
            else:
                # 为没有bbox数据的元素生成占位符bbox信息
                result = self._add_placeholder_bbox(result)
            return result
        
        # 如果数据是一个列表，检查第一个元素
        if isinstance(data, list) and len(data) > 0:
            # 如果是generator格式的数据列表，取第一个
            first_item = data[0]
            if isinstance(first_item, dict) and 'elements' in first_item:
                result = first_item.copy()
                if bbox_data:
                    result = self._add_bbox_to_elements(result, bbox_data)
                else:
                    result = self._add_placeholder_bbox(result)
                return result
            else:
                # 将列表转换为标准格式
                result = self._rows_to_standard_format(data, table_id)
                if bbox_data:
                    result = self._add_bbox_to_elements(result, bbox_data)
                else:
                    result = self._add_placeholder_bbox(result)
                return result
        
        # 尝试转换其他格式
        if isinstance(data, dict):
            if 'table' in data:
                # 如果有table字段，提取表格数据
                table_data = data['table']
                result = self._table_data_to_standard_format(table_data, table_id)
            elif 'rows' in data:
                # 如果直接有rows字段
                result = self._rows_to_standard_format(data['rows'], table_id)
            else:
                # 其他字典格式，尝试提取有用信息
                result = {
                    "elements": [
                        {
                            "type": "table",
                            "rows": self._extract_rows_from_any_format(data)
                        }
                    ],
                    "industry": data.get("industry", "通用"),
                    "source": "generator_engine",
                    "table_id": table_id
                }
        else:
            # 其他格式，转换为基本格式
            result = {
                "elements": [
                    {
                        "type": "table",
                        "rows": [[{"content": str(data)}]]
                    }
                ],
                "industry": "通用",
                "source": "generator_engine",
                "table_id": table_id
            }
        
        # 添加bbox信息
        if bbox_data:
            result = self._add_bbox_to_elements(result, bbox_data)
        else:
            result = self._add_placeholder_bbox(result)
        
        return result
    
    def _table_data_to_standard_format(self, table_data: Dict, table_id: int) -> Dict:
        """将表格数据转换为标准格式"""
        rows = []
        
        if 'rows' in table_data:
            for row_data in table_data['rows']:
                if isinstance(row_data, list):
                    # 简单的列表格式
                    row = [{"content": str(cell)} for cell in row_data]
                elif isinstance(row_data, dict) and 'cells' in row_data:
                    # 复杂的单元格格式
                    row = row_data['cells']
                else:
                    # 其他格式
                    row = [{"content": str(row_data)}]
                rows.append(row)
        
        return {
            "elements": [
                {
                    "type": "table",
                    "rows": rows
                }
            ],
            "industry": table_data.get("industry", "通用"),
            "source": "generator_engine",
            "table_id": table_id
        }
    
    def _rows_to_standard_format(self, rows_data: List, table_id: int) -> Dict:
        """将行数据转换为标准格式"""
        rows = []
        
        for row_data in rows_data:
            if isinstance(row_data, list):
                # 简单的列表格式
                row = [{"content": str(cell)} for cell in row_data]
            elif isinstance(row_data, dict):
                # 复杂格式，尝试提取内容
                if 'cells' in row_data:
                    row = row_data['cells']
                elif 'content' in row_data:
                    row = [{"content": str(row_data['content'])}]
                else:
                    # 将整个dict作为内容
                    row = [{"content": str(row_data)}]
            else:
                row = [{"content": str(row_data)}]
            rows.append(row)
        
        return {
            "elements": [
                {
                    "type": "table",
                    "rows": rows
                }
            ],
            "industry": "通用",
            "source": "generator_engine",
            "table_id": table_id
        }
    
    def _extract_rows_from_any_format(self, data: Any) -> List[List[Dict]]:
        """从任意格式中提取行数据"""
        if isinstance(data, list):
            rows = []
            for item in data:
                if isinstance(item, list):
                    row = [{"content": str(cell)} for cell in item]
                else:
                    row = [{"content": str(item)}]
                rows.append(row)
            return rows
        elif isinstance(data, dict):
            # 尝试从字典中提取信息
            content = json.dumps(data, ensure_ascii=False, indent=2)
            return [[{"content": content}]]
        else:
            return [[{"content": str(data)}]]
    
    def _add_bbox_to_elements(self, annotation_data: Dict, bbox_data: List[Dict]) -> Dict:
        """
        将bbox信息添加到annotation数据中

        Args:
            annotation_data: 标注数据
            bbox_data: Playwright提取的bbox信息列表

        Returns:
            Dict: 添加了bbox信息的标注数据
        """
        if not bbox_data or not annotation_data.get('elements'):
            return annotation_data

        result = annotation_data.copy()
        elements = result.get('elements', [])

        print(f"🔍 开始匹配bbox信息: {len(elements)} 个annotation元素, {len(bbox_data)} 个DOM元素")

        # 使用改进的匹配算法
        matched_elements = self._match_elements_with_bbox(elements, bbox_data)

        # 更新elements的bbox信息
        for i, element in enumerate(elements):
            if i < len(matched_elements) and matched_elements[i]:
                elements[i]['bbox'] = matched_elements[i]['bbox']
                print(f"✅ 匹配成功: {element.get('type')} -> bbox({matched_elements[i]['bbox']['x']}, {matched_elements[i]['bbox']['y']})")
            else:
                print(f"⚠️ 未找到匹配: {element.get('type')} - {str(element.get('content', ''))[:50]}")

        # 同时在顶层添加完整的bbox信息，供analyzer使用
        result['bbox_elements'] = bbox_data
        result['has_bbox'] = True

        return result

    def _match_elements_with_bbox(self, elements: List[Dict], bbox_data: List[Dict]) -> List[Dict]:
        """
        改进的元素与bbox匹配算法

        Args:
            elements: annotation元素列表
            bbox_data: DOM元素bbox信息列表

        Returns:
            List[Dict]: 匹配的bbox信息列表，与elements一一对应
        """
        matched_bboxes = [None] * len(elements)
        used_bbox_indices = set()

        # 第一轮：精确文本匹配
        for i, element in enumerate(elements):
            element_type = element.get('type', '')
            element_content = self._extract_element_text(element)

            if not element_content:
                continue

            best_match = None
            best_score = 0
            best_bbox_idx = -1

            for j, bbox_item in enumerate(bbox_data):
                if j in used_bbox_indices:
                    continue

                bbox_text = bbox_item.get('text', '').strip()
                if not bbox_text:
                    continue

                # 计算文本匹配度
                score = self._calculate_text_similarity(element_content, bbox_text)

                # 类型匹配加分
                if self._is_type_compatible(element_type, bbox_item.get('type', '')):
                    score += 0.3

                # 避免匹配table内部的元素给独立元素
                if self._is_table_internal_element(bbox_item) and element_type not in ['table']:
                    score *= 0.1  # 大幅降低分数

                # 避免重叠匹配：检查与已匹配元素的位置关系
                if score > 0.7 and not self._has_bbox_overlap_with_matched(bbox_item, matched_bboxes[:i]):
                    if score > best_score:
                        best_match = bbox_item
                        best_score = score
                        best_bbox_idx = j

            if best_match:
                matched_bboxes[i] = best_match
                used_bbox_indices.add(best_bbox_idx)
                print(f"🎯 精确匹配: {element_type} '{element_content[:30]}' -> '{best_match.get('text', '')[:30]}' (score: {best_score:.2f})")

        # 第二轮：类型匹配（为未匹配的元素）
        for i, element in enumerate(elements):
            if matched_bboxes[i] is not None:
                continue

            element_type = element.get('type', '')

            for j, bbox_item in enumerate(bbox_data):
                if j in used_bbox_indices:
                    continue

                if self._is_type_compatible(element_type, bbox_item.get('type', '')):
                    matched_bboxes[i] = bbox_item
                    used_bbox_indices.add(j)
                    print(f"🔗 类型匹配: {element_type} -> {bbox_item.get('type', '')}")
                    break

        # 第三轮：位置顺序匹配（为剩余未匹配的元素）
        unmatched_elements = [i for i, bbox in enumerate(matched_bboxes) if bbox is None]
        unmatched_bboxes = [i for i in range(len(bbox_data)) if i not in used_bbox_indices]

        for i, element_idx in enumerate(unmatched_elements):
            if i < len(unmatched_bboxes):
                bbox_idx = unmatched_bboxes[i]
                matched_bboxes[element_idx] = bbox_data[bbox_idx]
                used_bbox_indices.add(bbox_idx)
                print(f"📍 位置匹配: 元素{element_idx} -> bbox{bbox_idx}")

        return matched_bboxes

    def _extract_element_text(self, element: Dict) -> str:
        """
        从annotation元素中提取文本内容

        Args:
            element: annotation元素

        Returns:
            str: 提取的文本内容
        """
        element_type = element.get('type', '')

        if element_type == 'checkbox':
            # 复选框元素，提取key和选项信息
            key = element.get('key', '')
            options = element.get('options', [])
            if options:
                # 构建复选框文本，类似 "是否已知过敏史: ☑ 是 □ 否"
                option_texts = []
                for opt in options:
                    checked = opt.get('checked', False)
                    option_text = opt.get('option', '')
                    symbol = '☑' if checked else '□'
                    option_texts.append(f"{symbol} {option_text}")
                return f"{key}: {' '.join(option_texts)}"
            return key

        elif element_type == 'table':
            # 表格元素，提取所有单元格内容
            rows = element.get('rows', [])
            all_text = []
            for row in rows:
                for cell in row:
                    content = cell.get('content', '')
                    if isinstance(content, str):
                        all_text.append(content)
                    elif isinstance(content, dict):
                        # 处理复杂单元格内容（如嵌套的复选框）
                        if 'key' in content:
                            all_text.append(content.get('key', ''))
            return ''.join(all_text)

        elif element_type in ['title', 'paragraph']:
            # 标题和段落元素
            return element.get('content', '')

        elif element_type in ['signature', 'date']:
            # 签名和日期元素
            return element.get('content', '')

        else:
            # 其他类型元素
            content = element.get('content', '')
            if isinstance(content, str):
                return content
            elif isinstance(content, dict):
                return str(content)
            else:
                return str(content)

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的相似度

        Args:
            text1: 第一个文本
            text2: 第二个文本

        Returns:
            float: 相似度分数 (0-1)
        """
        if not text1 or not text2:
            return 0.0

        text1 = text1.strip()
        text2 = text2.strip()

        if text1 == text2:
            return 1.0

        # 检查包含关系
        if text1 in text2 or text2 in text1:
            shorter = min(len(text1), len(text2))
            longer = max(len(text1), len(text2))
            return shorter / longer

        # 检查关键词匹配
        words1 = set(text1.split())
        words2 = set(text2.split())

        if words1 and words2:
            intersection = words1.intersection(words2)
            union = words1.union(words2)
            return len(intersection) / len(union)

        return 0.0

    def _is_type_compatible(self, annotation_type: str, dom_type: str) -> bool:
        """
        检查annotation类型和DOM类型是否兼容

        Args:
            annotation_type: annotation元素类型
            dom_type: DOM元素类型

        Returns:
            bool: 是否兼容
        """
        # 类型映射关系
        type_mapping = {
            'title': ['title', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
            'paragraph': ['paragraph', 'p'],
            'table': ['table'],
            'checkbox': ['div'],  # 复选框通常渲染为div
            'signature': ['div'],  # 签名通常渲染为div
            'date': ['div'],      # 日期通常渲染为div
        }

        compatible_types = type_mapping.get(annotation_type, [])
        return dom_type in compatible_types or annotation_type == dom_type

    def _has_bbox_overlap_with_matched(self, bbox_item: Dict, matched_bboxes: List[Dict]) -> bool:
        """
        检查bbox是否与已匹配的bbox重叠

        Args:
            bbox_item: 待检查的bbox项
            matched_bboxes: 已匹配的bbox列表

        Returns:
            bool: 是否有重叠
        """
        if not bbox_item or 'bbox' not in bbox_item:
            return False

        current_bbox = bbox_item['bbox']

        for matched in matched_bboxes:
            if matched is None or 'bbox' not in matched:
                continue

            matched_bbox = matched['bbox']

            # 检查是否完全相同
            if (current_bbox['x'] == matched_bbox['x'] and
                current_bbox['y'] == matched_bbox['y'] and
                current_bbox['width'] == matched_bbox['width'] and
                current_bbox['height'] == matched_bbox['height']):
                return True

            # 检查是否有显著重叠（重叠面积超过较小元素的50%）
            overlap_area = self._calculate_bbox_overlap_area(current_bbox, matched_bbox)
            if overlap_area > 0:
                current_area = current_bbox['width'] * current_bbox['height']
                matched_area = matched_bbox['width'] * matched_bbox['height']
                min_area = min(current_area, matched_area)

                if overlap_area / min_area > 0.5:  # 重叠超过50%
                    return True

        return False

    def _calculate_bbox_overlap_area(self, bbox1: Dict, bbox2: Dict) -> float:
        """
        计算两个bbox的重叠面积

        Args:
            bbox1: 第一个bbox
            bbox2: 第二个bbox

        Returns:
            float: 重叠面积
        """
        x1_left, y1_top = bbox1['x'], bbox1['y']
        x1_right, y1_bottom = x1_left + bbox1['width'], y1_top + bbox1['height']

        x2_left, y2_top = bbox2['x'], bbox2['y']
        x2_right, y2_bottom = x2_left + bbox2['width'], y2_top + bbox2['height']

        # 计算重叠区域
        overlap_left = max(x1_left, x2_left)
        overlap_top = max(y1_top, y2_top)
        overlap_right = min(x1_right, x2_right)
        overlap_bottom = min(y1_bottom, y2_bottom)

        # 如果没有重叠
        if overlap_left >= overlap_right or overlap_top >= overlap_bottom:
            return 0.0

        return (overlap_right - overlap_left) * (overlap_bottom - overlap_top)

    def _is_table_internal_element(self, bbox_item: Dict) -> bool:
        """
        检查DOM元素是否是表格内部元素

        Args:
            bbox_item: DOM元素bbox信息

        Returns:
            bool: 是否是表格内部元素
        """
        element_type = bbox_item.get('type', '')
        tag = bbox_item.get('tag', '')

        # 表格内部元素类型
        table_internal_types = ['table_cell', 'td', 'th']
        table_internal_tags = ['td', 'th']

        return (element_type in table_internal_types or
                tag in table_internal_tags)

    def _find_bbox_data_for_json(self, json_file_path: str, json_data: Dict) -> List[Dict]:
        """
        为普通JSON文件查找对应的bbox信息
        
        Args:
            json_file_path: JSON文件路径
            json_data: JSON文件内容
            
        Returns:
            List[Dict]: bbox信息列表，如果找不到则返回空列表
        """
        try:
            # 尝试从同目录的其他文件中找到bbox信息
            # 1. 查找同目录下的train_data.json文件
            dir_path = os.path.dirname(json_file_path)
            train_data_path = os.path.join(dir_path, "train_data.json")
            
            if os.path.exists(train_data_path):
                with open(train_data_path, 'r', encoding='utf-8') as f:
                    train_data = json.load(f)
                
                # 查找匹配的数据项
                if isinstance(train_data, list):
                    for item in train_data:
                        if 'bbox_data' in item:
                            # 这里可以添加更复杂的匹配逻辑
                            # 目前简单返回第一个找到的bbox_data
                            return item['bbox_data']
            
            # 2. 尝试从文件名模式匹配对应的bbox文件
            base_name = os.path.splitext(os.path.basename(json_file_path))[0]
            bbox_file_path = os.path.join(dir_path, f"{base_name}_bbox.json")
            
            if os.path.exists(bbox_file_path):
                with open(bbox_file_path, 'r', encoding='utf-8') as f:
                    bbox_data = json.load(f)
                    if isinstance(bbox_data, list):
                        return bbox_data
            
            # 如果找不到bbox信息，返回空列表
            return []
            
        except Exception as e:
            print(f"⚠️ 查找bbox信息时出错: {e}")
            return []
    
    def _add_placeholder_bbox(self, annotation_data: Dict) -> Dict:
        """
        为没有bbox数据的annotation添加占位符bbox信息
        
        Args:
            annotation_data: 标注数据
            
        Returns:
            Dict: 添加了占位符bbox信息的标注数据
        """
        result = annotation_data.copy()
        
        if 'elements' in result and isinstance(result['elements'], list):
            # 为每个元素添加占位符bbox
            y_offset = 0
            for i, element in enumerate(result['elements']):
                # 根据元素类型设置不同的占位符尺寸
                if element.get('type') == 'title':
                    height = 40
                elif element.get('type') == 'table':
                    rows = element.get('rows', [])
                    height = max(30 * len(rows), 60)  # 估算表格高度
                elif element.get('type') == 'paragraph':
                    height = 25
                else:
                    height = 30
                
                # 创建占位符bbox
                placeholder_bbox = {
                    "x": 0,
                    "y": y_offset,
                    "width": 800,  # 假设页面宽度为800px
                    "height": height
                }
                
                result['elements'][i]['bbox'] = placeholder_bbox
                y_offset += height + 10  # 添加间距
            
            # 标记为占位符bbox
            result['bbox_elements'] = []
            result['has_bbox'] = False  # 标记为占位符
            result['bbox_source'] = "placeholder"
            print(f"✨ 为 {len(result['elements'])} 个元素添加了占位符bbox信息")
        
        return result