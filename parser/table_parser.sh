#!/bin/bash

# ===============================================
# 表格解析系统启动脚本
# 
# 职责：
# 1. 环境变量配置和验证
# 2. 目录结构初始化
# 3. 依赖检查和安装
# 4. Python虚拟环境激活
# 5. 调用Python主程序
# ===============================================

set -e  # 遇到错误时立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

# ===============================================
# 配置加载和验证
# ===============================================

# 保存命令行传入的重要环境变量
CMDLINE_DATASET_NAME="$DATASET_NAME"

# 加载配置文件
if [ -f "../config.env" ]; then
    print_info "加载配置文件..."
    source ../config.env
else
    print_error "未找到配置文件 ../config.env"
    exit 1
fi

# 恢复命令行传入的环境变量（命令行参数优先级最高）
if [ ! -z "$CMDLINE_DATASET_NAME" ]; then
    export DATASET_NAME="$CMDLINE_DATASET_NAME"
    print_info "使用命令行指定的测试集: ${DATASET_NAME}"
else
    export DATASET_NAME="${DATASET_NAME:-kingsoft}"
fi

print_success "当前测试集: ${DATASET_NAME}"

# ===============================================
# 目录结构配置
# ===============================================

# 基于测试集名称的目录结构
export DATASET_BASE_DIR="${PROJECT_ROOT_DIR}/dataset"
export DATASET_DIR="${DATASET_BASE_DIR}/${DATASET_NAME}"
export IMAGES_DIR="${DATASET_DIR}/images"
export PDF_DIR="${DATASET_DIR}/converted_pdfs"
export MONKEY_OCR_DIR="${DATASET_DIR}/monkey_ocr"
export GEN_DATA_DIR="${DATASET_DIR}/gen_data"
export MINERU_OUTPUT_DIR="${DATASET_DIR}/mineru"

export PARSE_RESULTS_BASE_DIR="${PROJECT_ROOT_DIR}/parse_results"
export PARSE_RESULTS_DIR="${PARSE_RESULTS_BASE_DIR}/${DATASET_NAME}"

export REPORTS_BASE_DIR="${PROJECT_ROOT_DIR}/reports"
export REPORTS_DIR="${REPORTS_BASE_DIR}/${DATASET_NAME}"

# 创建目录结构
print_info "初始化目录结构..."
mkdir -p "${IMAGES_DIR}"
mkdir -p "${PDF_DIR}"
mkdir -p "${MONKEY_OCR_DIR}"
mkdir -p "${GEN_DATA_DIR}"
mkdir -p "${MINERU_OUTPUT_DIR}"
mkdir -p "${PARSE_RESULTS_DIR}"
mkdir -p "${REPORTS_DIR}"

print_success "目录结构初始化完成:"
echo "   数据集: ${DATASET_DIR}"
echo "   解析结果: ${PARSE_RESULTS_DIR}"
echo "   报告: ${REPORTS_DIR}"

# ===============================================
# 系统依赖检查
# ===============================================

print_info "检查系统依赖..."

# 检查并安装 ImageMagick
if ! command -v convert &> /dev/null; then
    print_warning "ImageMagick 未安装，正在安装..."
    sudo apt-get update
    sudo apt-get install -y imagemagick
    print_success "ImageMagick 安装完成"
else
    print_success "ImageMagick 已安装"
fi

# 检查并安装 wkhtmltopdf
if ! command -v wkhtmltopdf &> /dev/null; then
    print_warning "wkhtmltopdf 未安装，正在安装..."
    sudo apt-get update
    sudo apt-get install -y wkhtmltopdf
    print_success "wkhtmltopdf 安装完成"
else
    print_success "wkhtmltopdf 已安装"
fi

# ===============================================
# Python 环境配置
# ===============================================

print_info "配置Python环境..."

# 激活虚拟环境
if [ -f "${PYTHON_VENV_PATH}" ]; then
    source ${PYTHON_VENV_PATH}
    print_success "Python虚拟环境已激活"
else
    print_error "Python虚拟环境路径无效: ${PYTHON_VENV_PATH}"
    exit 1
fi

# 设置SSL证书路径
if [ -f "${SSL_CERT_PATH}" ]; then
    export REQUESTS_CA_BUNDLE=${SSL_CERT_PATH}
    print_success "SSL证书路径已设置"
else
    print_warning "SSL证书路径无效: ${SSL_CERT_PATH}"
fi

# 设置 PYTHONPATH
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
export PYTHONPATH="${SCRIPT_DIR}/src:${PYTHONPATH}"
print_success "PYTHONPATH已设置: ${PYTHONPATH}"

# ===============================================
# 参数处理和主程序调用
# ===============================================

# 显示配置信息
print_info "当前配置:"
echo "   LLM服务类型: ${LLM_SERVICE_TYPE}"
echo "   LLM端点: ${CUSTOM_LLM_ENDPOINT}"
echo "   LLM使用代理: ${LLM_USE_PROXY}"
echo "   MonkeyOCR超时: ${MONKEY_OCR_TIMEOUT:-300} 秒"

# 显示使用帮助
show_usage() {
    cat << EOF

🚀 表格解析系统使用方法:

基本用法:
  ./table_parser.sh [选项]

选项:
  --mode <模式>           运行模式: full|parse|report (默认: full)
  --generate-data         强制重新生成数据
  --num-tables <数量>     生成表格数量 (默认: 2)
  --use-llm              使用LLM生成表格 (默认启用)
  --topics <主题...>      指定表格主题
  --generator-engine <引擎> 生成器引擎: tasks|generator (默认: generator)
  --debug                启用调试模式
  --help, -h             显示此帮助信息

环境变量:
  DATASET_NAME           测试集名称 (默认: kingsoft)

示例:
  # 使用默认配置运行完整流程
  ./table_parser.sh

  # 指定测试集运行
  DATASET_NAME=my_test ./table_parser.sh

  # 只执行解析，不生成数据和报告
  ./table_parser.sh --mode parse

  # 只生成报告
  ./table_parser.sh --mode report

  # 强制重新生成数据并运行完整流程
  ./table_parser.sh --generate-data --num-tables 5

  # 使用指定主题生成表格
  ./table_parser.sh --generate-data --topics "员工表" "销售表"

  # 启用调试模式
  ./table_parser.sh --debug

注意: 
  - 必须通过此脚本启动，不要直接运行Python文件
  - 脚本会自动处理环境配置、依赖检查等
  - 所有路径基于当前测试集自动配置

EOF
}

# 处理帮助参数
if [[ "$1" == "help" || "$1" == "-h" || "$1" == "--help" ]]; then
    show_usage
    exit 0
fi

# 调用Python主程序，传递所有参数
print_info "启动解析系统..."
print_info "执行命令: python src/main.py $@"

exec python src/main.py "$@"
