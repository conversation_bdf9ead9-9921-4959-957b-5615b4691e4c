import json
from prompts.system import train_system, user_template, assistant_template,assistant_template_csv,train_system_combined_error, train_system_mark_left_no_index,train_system_mark_left_no_index_csv
from converter.checkbox_mask import build_wrong_blocks
import os

def load_json_file(file_path):
    with open(file_path,"r", encoding="utf-8") as f:
        data = json.load(f)
    return data

def convert_answer_to_csv(data):
    if len(data) == 0:
        return ""
    # 构建表头
    csv = "line,wrong,refined\n"
    
    # 构建每一行数据
    for item in data:
        line = item.get('line', '')
        wrong = item.get('wrong', '')
        refined = item.get('refined', '')
        csv += f"{line},{wrong},{refined}\n"
    
    return csv



def build_llm_data(data, store_path):
    train_data = []
    
    for d in data:
        image = d['file_name']
        text = d['masked_markdown']
        new_wrong_blocks = d['wrong_blocks']

        # 这里将答案转换为markdown格式
        # answer = json.dumps(new_wrong_blocks, indent=4, ensure_ascii=False)
        answer = convert_answer_to_csv(new_wrong_blocks)


        messages= [
            # {"role": "user", "content": user_template.format(system=train_system, text=text)},
            {"role": "user", "content": user_template.format(system=train_system_mark_left_no_index_csv, text=text)},
            {"role": "assistant", "content": assistant_template_csv.format(answer=answer)}
        ]
        train = {
            "messages": messages,
            "images": [image]
        }
        train_data.append(train)
    with open(store_path, "w", encoding="utf-8") as f:
        json.dump(train_data, f, ensure_ascii=False, indent=4)


def get_train_test_data(data_dir, checkbox_length, complex=False, complex_type=None):
    data_path = os.path.join(data_dir, "train_data.json")
    json_path = os.path.join(data_dir, "json_mapping.json")

    train_data_list = load_json_file(data_path)
    json_mapping = load_json_file(json_path)

    train_data, test_data = build_wrong_blocks(train_data_list, json_mapping, checkbox_length=checkbox_length, complex=complex, complex_type=complex_type)
    store_name = data_path.split("/")[-1].split(".")[0]
    
    train_store_path = os.path.join(data_dir, f"llm_data_train_{checkbox_length}.json")
    test_store_path = os.path.join(data_dir, f"llm_data_test_{checkbox_length}.json")
    
    build_llm_data(train_data, train_store_path)
    build_llm_data(test_data, test_store_path)

def gen_short_long_data(data_dir, complex, complex_type=None):
    checkbox_length = ["short", "long"]
    for l in checkbox_length:
        get_train_test_data(data_dir, l, complex=complex, complex_type=complex_type)



if __name__ == "__main__":
    data_dir = os.getenv("GENERATOR_DATA_DIR", "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/data") + "/2025_06_19_16000"
    checkbox_length = ["short", "long"]
    for l in checkbox_length:
        get_train_test_data(data_dir, l)
