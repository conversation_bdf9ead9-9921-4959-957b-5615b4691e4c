from image_gen import gen_image_data, gen_complex_image_data
from llm_train_data_gen import gen_short_long_data
from datetime import date
import os
import time
from converter.image_noise_v1 import batch_process_images
import json
import random

data_path = os.getenv("GENERATOR_DATA_DIR", "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/data") + "/customized"

read_list = [
    "2025-06-19_2000",
    "2025-06-19_4000",
    "2025-06-19_6000",
    "2025-06-19_8000",
    "2025-06-19_10000",
    "2025-06-19_12000",
    "2025-06-19_14000"
    ]
sleep_second = 10 
complexd = True
# complex_type = "MARK"
# complex_type_list = ["mark",  "left", "miss", "all"]
complex_type_list = ["mark",  "left", "all"]

for i in range(10000000000):
    current_date = date.today()

    data_dir_list = os.listdir(data_path)

    for data_dir in data_dir_list:        
        if data_dir in read_list:
            continue
        for complex_type in complex_type_list:
            file_path = os.path.join(data_path, data_dir)

            try:
            
                if os.path.exists(os.path.join(file_path, "llm_gen_data.json")):
                    print(file_path)
                    # 生成图片数据
                    # print("开始生成图片")
                    # if complexd:
                    #     gen_complex_image_data(file_path, max_workers=200)
                    # else:
                    #     gen_image_data(file_path)
                    print("开始生成大模型训练数据")
                    # 生成大模型训练数据
                    
                    gen_short_long_data(file_path, complexd, complex_type)

                    # print("开始构造scaned风格数据")
                    # input_images = os.path.join(file_path, "images")
                    scanned_images = os.path.join(file_path, "scaned_images")
                    # watermark_images = os.path.join(file_path, "watermark_images")

                    # os.makedirs(scanned_images, exist_ok=True)
                    # os.makedirs(watermark_images, exist_ok=True)

                    # batch_process_images(input_images, scanned_images, watermark_images, None, 200)
                    
                    # 增加scanned，将所有的文件都放在一个json里面。
                    prefix = "llm_data"
                    typ = ["train", "test"]
                    length = ["long", "short"]
                    all_data = []

                    for t in typ:
                        for l in length:
                            json_file = os.path.join(file_path, f"{prefix}_{t}_{l}.json")
                            with open(json_file, "r") as f:
                                data = json.load(f)

                            ### 过滤以下很多X的数据
                            filter_data = []
                            for d in data:
                                if d['messages'][0]['content'].count("X") >= 3:
                                    continue
                                filter_data.append(d)
                            data = filter_data
                            ### 过滤X的数据

                            all_data.extend(data)

                            for d in data:
                                image = d['images'][0]
                                if os.path.exists(os.path.join(scanned_images, image)):
                                    scanned_data = {
                                        "messages": d['messages'],
                                        "images": [image.replace("images", "scaned_images")]
                                    }
                                    all_data.append(scanned_data)
                                
                    with open(os.path.join(file_path, f"all_train_data_{complex_type}_0721.json"), "w") as f:
                        json.dump(all_data, f, ensure_ascii=False, indent=2)

                    read_list.append(data_dir)            
            except:
                print("continue ")        


    print(f"sleep {sleep_second} seconds")
    time.sleep(sleep_second)


    


    