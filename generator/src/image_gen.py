from converter.json2markdown import json_to_markdown, get_checkbox_type
from converter.json2markdown_complex import json2html, body2html
from converter.text2img import convert_text2image, html2image
import json
import json
import concurrent.futures
import os
import random
import traceback

def process_doc(doc, i, image_dir):
    results = []
    try:
        # 主图像生成
        text = json_to_markdown(doc)
        
        image_path, bbox_data = convert_text2image(text, image_dir)
        results.append({
            "text": text,
            "image_path": image_path,
            "json_id": i,
            "bbox_data": bbox_data,  # 添加bbox信息
        })

        # 样本图像生成
        sample_list = get_checkbox_type(k=3)
        for s in sample_list:
            directional = s['direction']
            checkbox = s['checkbox']
            text = json_to_markdown(doc, directional, checkbox, 'normal')
            image_path, bbox_data = convert_text2image(text, image_dir)
            results.append({
                "text": text,
                "image_path": image_path,
                "json_id": i,
                "bbox_data": bbox_data,  # 添加bbox信息
            })
    except Exception as e:
        print(e, "skip")
        return None

    return i, doc, results

def process_doc_by_json(doc, i, image_dir):
    result = []
    try:
        checkbox_type_list = [
            {"selected":"☑", "unselected": "□"},
            {"selected":"☒", "unselected": "□"}
        ]
        for c in checkbox_type_list:
            direction = random.choice(["horizontal", "vetical"])
            text = json2html(doc, direction=direction, checkbox=c, style="normal")
            html = body2html(text)

            image_path, bbox_data = html2image(html, image_dir)
            print("save path: ", image_path)
            result.append({
                "text": text,
                "image_path": image_path,
                "json_id": i,
                "bbox_data": bbox_data,  # 添加bbox信息
            })
        # 样本图片生成
    except Exception as e:
        print(e, "skip")
        traceback.print_exc()
        return None

    return i, doc, result

def gen_complex_image_data(data_dir,  max_workers=100):
    image_dir = os.path.join(data_dir, "images")
    data_path = os.path.join(data_dir, "llm_gen_data.json")
    if not os.path.exists(image_dir):
        os.makedirs(image_dir)
    train_data = []
    json_mapping = {}

    with open(data_path, "r", encoding="utf-8") as f:
        data = json.load(f)
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [
                executor.submit(process_doc_by_json, doc, i, image_dir)
                for i, doc in enumerate(data)
            ]

            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                if result is not None:
                    i, doc, items = result
                    train_data.extend(items)
                    json_mapping[i] = doc

                    # 实时保存
                    with open(os.path.join(data_dir, "train_data.json"), "w", encoding="utf-8") as f_train:
                        json.dump(train_data, f_train, ensure_ascii=False, indent=4)
                    with open(os.path.join(data_dir, "json_mapping.json"), "w", encoding="utf-8") as f_map:
                        json.dump(json_mapping, f_map, ensure_ascii=False, indent=4)

    print("Total data generated:", len(train_data))


def gen_image_data(data_dir,  max_workers=100):
    image_dir = os.path.join(data_dir, "images")
    data_path = os.path.join(data_dir, "llm_gen_data.json")
    if not os.path.exists(image_dir):
        os.makedirs(image_dir)
    train_data = []
    json_mapping = {}

    with open(data_path, "r", encoding="utf-8") as f:
        data = json.load(f)
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [
                executor.submit(process_doc, doc, i, image_dir)
                for i, doc in enumerate(data)
            ]

            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                if result is not None:
                    i, doc, items = result
                    train_data.extend(items)
                    json_mapping[i] = doc

                    # 实时保存
                    with open(os.path.join(data_dir, "train_data.json"), "w", encoding="utf-8") as f_train:
                        json.dump(train_data, f_train, ensure_ascii=False, indent=4)
                    with open(os.path.join(data_dir, "json_mapping.json"), "w", encoding="utf-8") as f_map:
                        json.dump(json_mapping, f_map, ensure_ascii=False, indent=4)

    print("Total data generated:", len(train_data))

def main():
    data_path = os.getenv("GENERATOR_DATA_DIR", "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/data") + "/customized/2025-07-01_similar_data"
    gen_complex_image_data(data_path)


if __name__ == "__main__":
    main()