import json
import os
import json, os, sys
project_root = r"D:\kingsoft\document_gen"

# 添加到 sys.path
sys.path.append(project_root)

from wpsai_insight_common.llm_gateway.models.chat_data import MultiModalType, MultiModalContent, SftMultiModalImage, \
    SftMultiModalText,  SftMultiModalImageUrl, Message

from wpsai_insight_common.llm_gateway.llm import LLModelRpc, LLMChatStatus
from wpsai_insight_common.llm_gateway.models.public_model_gateway import PublicModelGateway
from wpsai_insight_common.llm_gateway.models.sft_model_gateway import SftModelGateway
import openpyxl
import time
from openai import OpenAI
from schema_proposal import schema_proposal

from src.utils.upload_image import gen_req_data

def get_json_object(text):
    start_index = text.find("```json")
    end_index = text.rfind("```")
    if start_index != -1 and end_index != -1:
        return text[start_index + 7:end_index]
    else:
        return text

def render_vl_prompt(prefix, suffix, schema, page_content, url_content):
    content = []
    text_content = ""
    for i, url in enumerate(url_content):
        # 放入图片
        content.append(MultiModalContent(type=MultiModalType.image_url, content=url))
        text_content += page_content[i] + "\n\n"

    prompt = prefix + suffix.format(context=text_content, schema=schema)
    content.append(MultiModalContent(type=MultiModalType.text, content=prompt))
    messages = [Message(role="user", content=content)]
    return messages



class LMModel(object):
    gateway = LLModelRpc.Gateway.Public
    selector: LLModelRpc.ModelSelector = None

    @classmethod
    def generate_response_mllm(
            cls, messages, top_k: float = 0.99, temperature: float = 0.01, is_stream: bool = False
    ) -> (LLMChatStatus, str):

        status, text =  LLModelRpc().multimodal(cls.gateway, messages=messages, selector=cls.selector, top_k = top_k,
                                                temperature = temperature, max_token=2000, is_stream=is_stream)
        if status != LLMChatStatus.OK:
            return status, ""


        return LLMChatStatus.OK, text

    @classmethod
    async def async_generate_response_mllm(
            cls, messages, top_k: float = 0.99, temperature: float = 0.01, is_stream: bool = False
    ) -> (LLMChatStatus, str):
        res = await LLModelRpc().async_multimodal(LLModelRpc.Gateway.Public, messages=messages, selector=cls.selector, top_k=top_k,max_token=10000,stream=True)
        final_res = ""
        async for t in res:
            final_res += t.text
            print(t.text)
        return LLMChatStatus.OK, final_res

    
    
    @classmethod
    def generate_response(
            cls, messages: str = None, top_k: float = 0.99, temperature: float = 0.01
    ) -> (LLMChatStatus, str):
        status, text = LLModelRpc().chat_text(cls.gateway,messages=messages, selector=cls.selector, top_k = top_k, temperature = temperature)
        if status != LLMChatStatus.OK:
            return status, ""

        return LLMChatStatus.OK, text

async def gen(messages, is_vl_model=False, is_stream=True,model="gpt-4o"):
    pub_conf = PublicModelGateway.Conf(
        host="http://ai-gateway.wps.cn",
        token="I26OQDZMT3pj2K3IhcRsUhRnlpG7PW4F",
        uid="9047",
        product_name="wps-kanmail-qa",
        intention_code="aigctest",
        provider="zhipu-zone",
        model="chatglm-130b-wps-128k",
        version=None,
        multimodal_provider="ali",
        multimodal_model="qwen-vl-max-0809",
        multimodal_version=None,
        sec_from="AI_DRIVE_KNOWLEDGE",
    )
    sft_conf = SftModelGateway.Conf(
        host="http://kmd-api.kas.wps.cn",
        multimodal_host="http://kmd-api.kas.wps.cn/api",
        token = 'sk-e3fb5ed3b4c342c980d2b30d0dd920c8'
    )
    LLModelRpc().create_models(pub_conf, sft_conf, None, pool_max=10)
    llm_model = {
        "gpt-4o": {
            "model": "gpt-4o",
            "provider": "azure",
            "version":"2024-05-13"
        },
        "qwen-max": {
            "model": "qwen-max",
            "provider": "ali",
            "version":"",
        },
        "gemini-2.5-pro-preview-05-06": {
            "model": "gemini-2.5-pro-preview-05-06",
            "provider": "google",
            "version":""
        },
        "qwen-vl-max": {
            "model": "qwen-vl-max-0809",
            "provider": "ali",
            "version":""
        },
        "o3": {
            "model": "o3",
            "provider": "azure",
            "version":"2025-04-16"
        },
        "claude-3-7-sonnet": {
            "model": "claude-3-7-sonnet",
            "provider": "aws",
            "version": "20250219-v1:0"
        },
        "gemini-2.5-flash": {
            "model": "gemini-2.5-flash",
            "provider": "google",
            "version": ""
        },
        "gemini-2.5-pro-preview-06-05":{
            "model":"gemini-2.5-pro-preview-06-05",
            "provider": "google",
            "version": ""
        }
    }
    
    
    if is_vl_model:
        
        LMModel.gateway = LLModelRpc.Gateway.Public
        LMModel.selector = LLModelRpc.ModelSelector(
            **llm_model[model])
        if is_stream:
            status, text =await LMModel.async_generate_response_mllm(messages) 
        else:
            status, text = LMModel.generate_response_mllm(messages)
    else:
        
        LMModel.gateway = LLModelRpc.Gateway.Public
        LMModel.selector = LLModelRpc.ModelSelector(
            **llm_model[model])
        
        status, text = LMModel.generate_response(messages)
    return status, text


# 拿到dst结果
file_name = "凯莱英-retrieval.json"
trail_file_name = "test-SAE报告-首次-扫描.pdf"


def get_content_infos(file_name):
    file_infos = {}
    with open(file_name, "r",encoding='utf-8') as f:
        data = json.load(f)

        for k, v in data.items():
            v = v['ocr_results']
            print(v.keys())
            images = v['Image']
            pages = v['Pages']
            page_mapping = {}

            for i in range(len(images)):
                if i not in page_mapping:
                    page_mapping[i] = {}
                page_mapping[i]['image_url'] = [m['url'] for m in images if m['page_num']==i][0]
                page_mapping[i]['page_content'] = [m['content'] for m in pages if m['page_num']==i]

            file_infos[k] = page_mapping

    return file_infos


async def gen_schema():
    prompt = """请你帮我设计一个json schema，这个json schema需要表达的是一个文档的所有元素，按顺序排列。
文档的元素包含：
1. 多级标题，一级，二级等标题。
2. 段落内容，文本文字。
3. 复选框，复选框一般都是key，和option list组成，key代表复选框的名称，option list代表复选框的选项， 还有一个checked的状态，表示这个复选框是否被勾选。复选框可能单独存在，也可能是存在于表格中。
4. 列表，list of items。
5. 表格，简单表格：一般是二维数组，第一维代表行，第二维代表列；复杂表格，存在表格拼接的情况，存在跨行跨列的情况；所以你在设计的时候需要加入跨行跨列的信息（rowspan，colspan), 此外表格内的元素可能是纯文字，也可能是复选框列表，也有可能是列表。
6. 签名，日期等，一般在文档的最后面。

请帮我设计一个这样的json schema，同时输出一个python函数，这个python函数需要实现将符合这个json schema格式的文本内容转换为一个html。
    
    你的输出需要包含两部分：
    ```json
    这里输出你设计的json schema。
    ```

    ```python
    def convert_json_to_html(json_object):
        # 这里实现将符合这个json schema格式的文本内容转换为一个html
        return html
    ```
    """

    messages = [Message(role="user", content=[MultiModalContent(type=MultiModalType.text, content=prompt)])]
    print(messages)

    status, text = await gen(messages=messages, is_vl_model=True, is_stream=True)
    return status, text


async def gen_json_schema(image_url, page_content):
    schema = {
    "type": "object",
    "properties": {
        "elements": {
            "type": "array",
            "items": {
                "oneOf": [
                    schema_proposal['title_schema'],
                    schema_proposal['paragraph_schema'],
                    schema_proposal['checkbox_schema'],
                    schema_proposal['list_schema'],
                    schema_proposal['table_schema'],
                    schema_proposal['signature_schema'],
                    schema_proposal['date_schema'],
                ]         
            }
        }
    },
    "definitions": {
        "checkbox": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string"
                },
                "options": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "option": {
                                "type": "string"
                            },
                            "checked": {
                                "type": "boolean"
                            }
                        }
                    }
                }
            }
        },
        "list": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        }
    },
    "required": [
        "elements"
    ]
}

    prefix = f"""你现在需要根据用户提供的图片和我给定的json schema，ocr内容来生成json对象， ocr的内容可能存在错误，请以图片为准。

    你要特别关注ocr中的复选框内容，不要丢失复选框内容信息.

    wire有线表的定义：
        且每一个cell都是用线分割开的。
    cell-wirelss无线表的定义：
        整个表格外部是有边框的， 每一个cell之间没有边框。
    wireless无线表的定义：
        整个表格都没有边框。

    你要特别关注表格的样式，根据上面定义的三种表格类型，来判断表格的风格是 ["wire", "inner-wireless", "wireless"] 中的哪种。
    
    ## json schema
    {json.dumps(schema, ensure_ascii=False, indent=2)}
    

    ```json
    这里输出你通过图片和OCR内容，json schema抽象出来的json对象, 如果未提供OCR内容,请根据图片内容来分析。
    ```

    """


    suffix = f"""## OCR 内容
    {page_content}

请根据要求输出相应的json。
    """    

    content = []
    content.append(MultiModalContent(type=MultiModalType.image_url, content=image_url))
    content.append(MultiModalContent(type=MultiModalType.text, content=prefix+suffix))

    # content.append(MultiModalContent(type=MultiModalType.text, content="你好"))

    messages = [Message(role="user", content=content)]
    print("messages\n", messages)

    status, text = await gen(messages=messages, is_vl_model=True, is_stream=True)

    if status == LLMChatStatus.OK:
        data_json = get_json_object(text)
        try:
            data_json = json.loads(data_json)
        except:
            return None
        return data_json
    else:
        return None

async def pipeline(file_name):
    # 拿到dst结果
    """
    {
        "file_name": {
            0: {
                "image_url": "",
                "page_content": ""
            }
        }
    }
    """
    data_json = []
    file_page_info_mapping = get_content_infos(file_name)

    for file, page_info in file_page_info_mapping.items():
        # todo delete
        if trail_file_name in file:
            continue

        # 这里我们需要对拿到的图片信息和ocr数据去做数据框架的生成。
        for page_num, info in page_info.items():
            json_object = await gen_json_schema(**info)
            if json_object != None:
                data_json.append(json_object)

            # 将数据存下来
            with open("kailaiying_rest.json", "w", encoding="utf-8") as f:
                json.dump(data_json, f, ensure_ascii=False, indent=2)

async def table_pipeline(table_ocr, table_path):
#     table_ocr = """<h1>成品检验报告书</h1><table border="1"><tr><td rowspan="1" colspan="4">检验编号：S-FLP-2411001</td></tr><tr><td rowspan="1" colspan="1">品名</td><td rowspan="1" colspan="1">非奈利酮片</td><td rowspan="1" colspan="1">批号</td><td rowspan="1" colspan="1">241101</td></tr><tr><td rowspan="1" colspan="1">规格</td><td rowspan="1" colspan="1">10mg</td><td rowspan="1" colspan="1">请验单位</td><td rowspan="1" colspan="1">生产部</td></tr><tr><td rowspan="1" colspan="1">数量</td><td rowspan="1" colspan="1">25.7089万片</td><td rowspan="1" colspan="1">生产日期</td><td rowspan="1" colspan="1">2024年11月04日</td></tr><tr><td rowspan="1" colspan="1">有效期至</td><td rowspan="1" colspan="1">2027年11月03日</td><td rowspan="1" colspan="1">报告日期</td><td rowspan="1" colspan="1">2024年11月23日</td></tr><tr><td rowspan="1" colspan="1">检验依据</td><td rowspan="1" colspan="3">TS-QA-S-FLP、《中国药典》2020年版</td></tr></table><table><tr><td rowspan="1" colspan="1">检验项目</td><td rowspan="1" colspan="1">检验标准</td><td rowspan="1" colspan="1">检验结果</td></tr><tr><td rowspan="1" colspan="1">【性状】</td><td rowspan="1" colspan="1">本品为粉红色长椭圆形薄膜衣片，除去包衣后显白色或类白色</td><td rowspan="1" colspan="1">粉红色长椭圆形薄膜衣片，除去包衣后显白色</td></tr><tr><td rowspan="1" colspan="3">【鉴别】</td></tr><tr><td rowspan="1" colspan="1">鉴别(1)</td><td rowspan="1" colspan="1">在含量项下记录的色谱图中，供试品溶液主峰的保留时间应与对照品溶液主峰的保留时间一致</td><td rowspan="1" colspan="1">符合规定</td></tr><tr><td rowspan="1" colspan="1">鉴别(2)</td><td rowspan="1" colspan="1">在含量项下记录的紫外光谱图中，供试品溶液主峰在200-400nm波长范围内的紫外光吸收图谱应与对照品溶液主峰的紫外光吸收图谱一致</td><td rowspan="1" colspan="1">符合规定</td></tr><tr><td rowspan="1" colspan="3">【检查】</td></tr><tr><td rowspan="3" colspan="1">有关物质</td><td rowspan="1" colspan="1">杂质3≤0.5%</td><td rowspan="1" colspan="1">0.047%</td></tr><tr><td rowspan="1" colspan="1">其他单个非特定降解杂质≤0.2%</td><td rowspan="1" colspan="1">0.046%</td></tr><tr><td rowspan="1" colspan="1">降解杂质总量≤1.0%</td><td rowspan="1" colspan="1">0.17%</td></tr><tr><td rowspan="1" colspan="1">含量均匀度</td><td rowspan="1" colspan="1">A+2.2S≤15.0</td><td rowspan="1" colspan="1">4.39</td></tr><tr><td rowspan="1" colspan="1">溶出度</td><td rowspan="1" colspan="1">≥标示量的85%</td><td rowspan="1" colspan="1">95% 97% 97%
# 97% 99% 96%
# 96% 97% 95%
# 97% 97% 97%</td></tr><tr><td rowspan="1" colspan="1">【含量测定】</td><td rowspan="1" colspan="1">本品含非奈利酮(C₂₁H₂₂N₄O₃)应为标示量的95.0%~105.0%</td><td rowspan="1" colspan="1">103.7%</td></tr><tr><td rowspan="1" colspan="3">【微生物限度】</td></tr><tr><td rowspan="1" colspan="1">需氧菌总数</td><td rowspan="1" colspan="1">每1g不得过10³cfu</td><td rowspan="1" colspan="1"><10cfu/g</td></tr><tr><td rowspan="1" colspan="1">霉菌和酵母菌总数</td><td rowspan="1" colspan="1">每1g不得过10²cfu</td><td rowspan="1" colspan="1"><10cfu/g</td></tr><tr><td rowspan="1" colspan="1">大肠埃希菌</td><td rowspan="1" colspan="1">每1g不得检出</td><td rowspan="1" colspan="1">未检出</td></tr><tr><td rowspan="1" colspan="1">结论：</td><td rowspan="1" colspan="1">本品按TS-QA-S-FLP、《中国药典》2020年版检验，结果符合规定。</td></tr></table>"""
    table_ocr = """<table border="1" style="border-collapse: collapse; width: 80%; margin: 20px auto;">
  <thead>
    <tr>
      <th colspan="5">2021年</th>
    </tr>
    <tr>
      <th>阶段</th>
      <th>第1阶段<br/>12个月<br/>预期信用损失</th>
      <th>第2阶段<br/>整个存续期<br/>预期信用损失</th>
      <th>第3阶段<br/>整个存续期<br/>预期信用损失</th>
      <th>总计</th>
    </tr>
  </thead>
  <tbody>
    <!-- 第一行 -->
    <tr>
      <td>2021年1月1日</td>
      <td>43,426</td>
      <td>32,870</td>
      <td>62,978</td>
      <td>139,274</td>
    </tr>

    <!-- 本年转移 -->
    <tr>
      <td rowspan="4">本年转移：</td>
      <td>(142)</td>
      <td>142</td>
      <td>-</td>
      <td>-</td>
    </tr>
    <tr>
      <td>(391)</td>
      <td>-</td>
      <td>391</td>
      <td>-</td>
    </tr>
    <tr>
      <td>-</td>
      <td>(7,598)</td>
      <td>7,598</td>
      <td>-</td>
    </tr>
    <tr>
      <td>14,593</td>
      <td>13,243</td>
      <td>34,951</td>
      <td>62,787</td>
    </tr>

    <!-- 本年核销及转让 -->
    <tr>
      <td>本年核销及转让</td>
      <td>-</td>
      <td>-</td>
      <td>(47,151)</td>
      <td>(47,151)</td>
    </tr>

    <!-- 收回已核销贷款 -->
    <tr>
      <td>收回已核销贷款</td>
      <td>-</td>
      <td>-</td>
      <td>6,324</td>
      <td>6,324</td>
    </tr>

    <!-- 其他变动 -->
    <tr>
      <td>其他变动</td>
      <td>(83)</td>
      <td>235</td>
      <td>(1,297)</td>
      <td>(1,145)</td>
    </tr>

    <!-- 2021年12月31日 -->
    <tr>
      <td>2021年12月31日</td>
      <td>57,403</td>
      <td>38,892</td>
      <td>63,794</td>
      <td>160,089</td>
    </tr>
  </tbody>
</table>"""

    table_path = r"D:\kingsoft\document_gen\12.jpg"

    url, key_name, file_type = gen_req_data(table_path)

    json_object = await gen_json_schema(image_url=url, page_content=table_ocr)

    print(json_object)

    with open("table_build.json", "w", encoding="utf-8") as f:
        json.dump(json_object, f, ensure_ascii=False, indent=2)







if __name__ == "__main__":
    model = "claude-3-7-sonnet"
    model = "qwen-vl-max"
    model = "gemini-2.5-flash"
    model = "gemini-2.5-pro-preview-06-05"
    model = "gpt-4o"

    import asyncio
    # asyncio.run(pipeline(file_name=file_name))
    # asyncio.run(gen_schema())
    asyncio.run(table_pipeline(None, None))