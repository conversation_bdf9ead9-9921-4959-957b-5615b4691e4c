import json
import os
import json, os, sys
project_root = r"D:\kingsoft\document_gen"

# 添加到 sys.path
sys.path.append(project_root)

from wpsai_insight_common.llm_gateway.models.chat_data import MultiModalType, MultiModalContent, SftMultiModalImage, \
    SftMultiModalText,  SftMultiModalImageUrl, Message

from wpsai_insight_common.llm_gateway.llm import LLModelRpc, LLMChatStatus
from wpsai_insight_common.llm_gateway.models.public_model_gateway import PublicModelGateway
from wpsai_insight_common.llm_gateway.models.sft_model_gateway import SftModelGateway
import openpyxl
import time
from openai import OpenAI
from schema_proposal import schema_proposal
from pipeline import gen
from src.utils.upload_image import gen_req_data
from src.converter.text2img import async_html2image


html_gen = """你现在是一名高级的前端开发工程师，需要根据用户提供的文档，用html数据将用户的文档表示出来。

## 文档内容
    1. 标题
    2. 表格
    3. 段落文本
    4. 图像
    5. 公式
    6. 列表
    7. 复选框
    8. 单选按钮
    9. 签名
    10. 图表
    11. 页眉/页脚
    12. 图注/表注
    13. 页码

## 要求
1. 你在用html构造文档时，仅需要考虑整体的结构和框架以及文字的准确性位置，不需要考虑图片，图片内容。
2. 你的输出为一个html内容，需要包含css样式, 你需要特别注意表格的样式，如表格的边框有无，下划线等。
3. 你需要尽可能还原图片内容和结构，禁止添加或删除内容。

请输出：
```html
"""

def get_html_content(html):
    return html.replace("```html", "").replace("```", "")

async def llm_req(text, image_url):
    content = []
    content.append(MultiModalContent(type=MultiModalType.image_url, content=image_url))
    content.append(MultiModalContent(type=MultiModalType.text, content=text))
    
    messages = [Message(role="user", content=content)]
    print("messages\n", messages)


    status, text = await gen(messages, is_vl_model=True, is_stream=True)

    if status == LLMChatStatus.OK:
        data_html = get_html_content(text)

        await async_html2image(data_html, ".")


async def main():
        table_path = r"D:\kingsoft\document_gen\travel_ticket.png"

        url, key_name, file_type = gen_req_data(table_path)
        image_url = url
        await llm_req(html_gen, image_url)

if __name__ == "__main__":
    model = "gpt-4o"
    
    import asyncio


    asyncio.run(main())
        








