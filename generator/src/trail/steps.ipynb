{"cells": [{"cell_type": "code", "execution_count": 30, "id": "bb1103bd-8499-4b8a-bab2-5af59a12bdc6", "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "\n", "from wpsai_insight_common.llm_gateway.models.chat_data import MultiModalType, MultiModalContent, SftMultiModalImage, \\\n", "    SftMultiModalText,  SftMultiModalImageUrl, Message\n", "\n", "from wpsai_insight_common.llm_gateway.llm import LLModelRpc, LLMChatStatus\n", "from wpsai_insight_common.llm_gateway.models.public_model_gateway import PublicModelGateway\n", "from wpsai_insight_common.llm_gateway.models.sft_model_gateway import SftModelGateway\n", "import openpyxl\n", "import time\n", "from openai import OpenAI\n", "\n", "\n", "def render_vl_prompt(prefix, suffix, schema, page_content, url_content):\n", "    content = []\n", "    text_content = \"\"\n", "    for i, url in enumerate(url_content):\n", "        # 放入图片\n", "        content.append(MultiModalContent(type=MultiModalType.image_url, content=url))\n", "        text_content += page_content[i] + \"\\n\\n\"\n", "\n", "    prompt = prefix + suffix.format(context=text_content, schema=schema)\n", "    content.append(MultiModalContent(type=MultiModalType.text, content=prompt))\n", "    messages = [Message(role=\"user\", content=content)]\n", "    return messages\n", "\n", "\n", "\n", "class LMModel(object):\n", "    gateway = LLModelRpc.Gateway.Public\n", "    selector: LLModelRpc.ModelSelector = None\n", "\n", "    @classmethod\n", "    def generate_response_mllm(\n", "            cls, messages, top_k: float = 0.99, temperature: float = 0.01, is_stream: bool = False\n", "    ) -> (LLMChatStatus, str):\n", "\n", "        status, text =  LLModelRpc().multimodal(cls.gateway, messages=messages, selector=cls.selector, top_k = top_k,\n", "                                                temperature = temperature, max_token=2000, is_stream=is_stream)\n", "        if status != LLMChatStatus.OK:\n", "            return status, \"\"\n", "\n", "\n", "        return LLMChatStatus.OK, text\n", "\n", "    @classmethod\n", "    async def async_generate_response_mllm(\n", "            cls, messages, top_k: float = 0.99, temperature: float = 0.01, is_stream: bool = False\n", "    ) -> (LLMChatStatus, str):\n", "        res = await LLModelRpc().async_chat_text_stream(LLModelRpc.Gateway.Public, cls, messages, top_k= 0.99, temperature = 0.01, max_token=20000)\n", "        final_res = \"\"\n", "        async for t in res:\n", "            final_res += t.text\n", "        return LLMChatStatus.OK, final_res\n", "\n", "    \n", "    \n", "    @classmethod\n", "    def generate_response(\n", "            cls, messages: str = None, top_k: float = 0.99, temperature: float = 0.01\n", "    ) -> (LLMChatStatus, str):\n", "        status, text = LLModelRpc().chat_text(cls.gateway,messages=messages, selector=cls.selector, top_k = top_k, temperature = temperature)\n", "        if status != LLMChatStatus.OK:\n", "            return status, \"\"\n", "\n", "        return LLMChatStatus.OK, text\n", "\n", "async def gen(messages, is_vl_model=False, is_stream=True):\n", "    pub_conf = PublicModelGateway.Conf(\n", "        host=\"http://ai-gateway.wps.cn\",\n", "        token=\"I26OQDZMT3pj2K3IhcRsUhRnlpG7PW4F\",\n", "        uid=\"9047\",\n", "        product_name=\"wps-kanmail-qa\",\n", "        intention_code=\"aigctest\",\n", "        provider=\"zhipu-zone\",\n", "        model=\"chatglm-130b-wps-128k\",\n", "        version=None,\n", "        multimodal_provider=\"ali\",\n", "        multimodal_model=\"qwen-vl-max-0809\",\n", "        multimodal_version=None,\n", "        sec_from=\"AI_DRIVE_KNOWLEDGE\",\n", "    )\n", "    sft_conf = SftModelGateway.Conf(\n", "        host=\"http://kmd-api.kas.wps.cn\",\n", "        multimodal_host=\"http://kmd-api.kas.wps.cn/api\",\n", "        token = 'sk-e3fb5ed3b4c342c980d2b30d0dd920c8'\n", "    )\n", "    LLModelRpc().create_models(pub_conf, sft_conf, None, pool_max=10)\n", "    llm_model = {\n", "        \"gpt-4o\": {\n", "            \"model\": \"gpt-4o\",\n", "            \"provider\": \"azure\",\n", "            \"version\":\"2024-05-13\"\n", "        },\n", "        \"qwen-max\": {\n", "            \"model\": \"qwen-max\",\n", "            \"provider\": \"ali\",\n", "            \"version\":\"\",\n", "        },\n", "        \"gemini-2.5-pro-preview-05-06\": {\n", "            \"model\": \"gemini-2.5-pro-preview-05-06\",\n", "            \"provider\": \"google\",\n", "            \"version\":\"\"\n", "        },\n", "        \"qwen-vl-max\": {\n", "            \"model\": \"qwen-vl-max-0809\",\n", "            \"provider\": \"ali\",\n", "            \"version\":\"\"\n", "        },\n", "        \"o3\": {\n", "            \"model\": \"o3\",\n", "            \"provider\": \"azure\",\n", "            \"version\":\"2025-04-16\"\n", "        },\n", "        \"claude-3-7-sonnet\": {\n", "            \"model\": \"claude-3-7-sonnet\",\n", "            \"provider\": \"aws\",\n", "            \"version\": \"20250219-v1:0\"\n", "        }\n", "    }\n", "    \n", "    \n", "    if is_vl_model:\n", "        \n", "        LMModel.gateway = LLModelRpc.Gateway.Public\n", "        LMModel.selector = LLModelRpc.ModelSelector(\n", "            **llm_model[model])\n", "        if is_stream:\n", "            status, text =await LMModel.async_generate_response_mllm(messages) \n", "        else:\n", "            status, text = LMModel.generate_response_mllm(messages)\n", "    else:\n", "        \n", "        LMModel.gateway = LLModelRpc.Gateway.Public\n", "        LMModel.selector = LLModelRpc.ModelSelector(\n", "            **llm_model[model])\n", "        \n", "        status, text = LMModel.generate_response(messages)\n", "    return status, text\n", "\n", "\n", "# 拿到dst结果\n", "file_name = \"kailaiying.json\"\n", "trail_file_name = \"test-SAE报告-首次-扫描.pdf\"\n", "\n", "\n", "def get_content_infos(file_name):\n", "    file_infos = {}\n", "    with open(file_name, \"r\",encoding='utf-8') as f:\n", "        data = json.load(f)\n", "\n", "        for k, v in data.items():\n", "            v = v['ocr_results']\n", "            print(v.keys())\n", "            images = v['Image']\n", "            pages = v['Pages']\n", "            page_mapping = {}\n", "\n", "            for i in range(len(images)):\n", "                if i not in page_mapping:\n", "                    page_mapping[i] = {}\n", "                page_mapping[i]['image_url'] = [m['url'] for m in images if m['page_num']==i][0]\n", "                page_mapping[i]['page_content'] = [m['content'] for m in pages if m['page_num']==i]\n", "\n", "            file_infos[k] = page_mapping\n", "\n", "    return file_infos\n", "\n", "async def gen_json_schema(image_url, page_content):\n", "    prefix = \"\"\"你现在需要根据用户提供的图片和其OCR内容抽象出一个json，同时你还需要输出一个python函数，这个python函数需要实现将这个json对象重新转换为图片的OCR内容。\n", "\n", "    该json需要包含图片的各种元素，例如标题，段落，表格，图片，复选框等。\n", "    1. 表格信息需要包含跨行和跨列信息，rowspan，colspan。\n", "    2. 复选框可能作为表格内容的一个cell也可能独立存在于文中，你还需要用字段来标识复选框的状态，checked=True|False。\n", "    3. 有些部分是手写体的文字，你也需要用一个标识来进行标记，例如 handwriting=True|False。\n", "\n", "    你的输出需要包含两部分：\n", "    ```json\n", "    这里输出你通过图片和OCR内容抽象出来的json对象\n", "    ```\n", "\n", "    ```python\n", "    这里输出你将json对象重新转换为图片的OCR内容的python函数，输入参数为json_object, 输出对象为一个html，这个html的结构和内容需要尽可能和图片和原ocr内容保持一致。\n", "    ```\n", "\n", "    \"\"\"\n", "\n", "\n", "    suffix = f\"\"\"## OCR 内容\n", "    {page_content}\n", "\n", "请根据要求输出相应的json和python内容。\n", "    \"\"\"    \n", "\n", "    content = []\n", "    content.append(MultiModalContent(type=MultiModalType.image_url, content=image_url))\n", "    content.append(MultiModalContent(type=MultiModalType.text, content=prefix+suffix))\n", "\n", "    # content.append(MultiModalContent(type=MultiModalType.text, content=\"你好\"))\n", "\n", "    messages = [Message(role=\"user\", content=content)]\n", "\n", "    print(messages)\n", "\n", "    status, text = await gen(messages=messages, is_vl_model=True, is_stream=True)\n", "\n", "    print(status, text)\n", "    return status, text\n", "\n", "async def pipeline(file_name):\n", "    # 拿到dst结果\n", "    \"\"\"\n", "    {\n", "        \"file_name\": {\n", "            0: {\n", "                \"image_url\": \"\",\n", "                \"page_content\": \"\"\n", "            }\n", "        }\n", "    }\n", "    \"\"\"\n", "    file_page_info_mapping = get_content_infos(file_name)\n", "\n", "    for file, page_info in file_page_info_mapping.items():\n", "        # todo delete\n", "        if trail_file_name not in file:\n", "            continue\n", "\n", "        # 这里我们需要对拿到的图片信息和ocr数据去做数据框架的生成。\n", "        for page_num, info in page_info.items():\n", "            await gen_json_schema(**info)\n", "            break\n", "        break\n", "\n", "\n", "        # 拿到数据框架之后，将对应的数据框架保存。\n", "\n", "\n", "    # 对每一个数据框架去生成数据\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 32, "id": "1bf9beae-5a44-411a-9393-abcaf9d42d2e", "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "asyncio.run() cannot be called from a running event loop", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[32], line 5\u001b[0m\n\u001b[0;32m      3\u001b[0m model \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mqwen-vl-max\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mas<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m----> 5\u001b[0m \u001b[43mas<PERSON>io\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpipeline\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfile_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfile_name\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\miniconda3\\envs\\doc-extract\\lib\\asyncio\\runners.py:33\u001b[0m, in \u001b[0;36mrun\u001b[1;34m(main, debug)\u001b[0m\n\u001b[0;32m      9\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Execute the coroutine and return the result.\u001b[39;00m\n\u001b[0;32m     10\u001b[0m \n\u001b[0;32m     11\u001b[0m \u001b[38;5;124;03mThis function runs the passed coroutine, taking care of\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     30\u001b[0m \u001b[38;5;124;03m    asyncio.run(main())\u001b[39;00m\n\u001b[0;32m     31\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m     32\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m events\u001b[38;5;241m.\u001b[39m_get_running_loop() \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m---> 33\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[0;32m     34\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124masyncio.run() cannot be called from a running event loop\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     36\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m coroutines\u001b[38;5;241m.\u001b[39miscoroutine(main):\n\u001b[0;32m     37\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124ma coroutine was expected, got \u001b[39m\u001b[38;5;132;01m{!r}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(main))\n", "\u001b[1;31mRuntimeError\u001b[0m: asyncio.run() cannot be called from a running event loop"]}], "source": ["if __name__ == \"__main__\":\n", "    model = \"claude-3-7-sonnet\"\n", "    model = \"qwen-vl-max\"\n", "    import asyncio\n", "    asyncio.run(pipeline(file_name=file_name))"]}, {"cell_type": "code", "execution_count": null, "id": "fb467a54-b5e3-4993-ba39-95af2c4084f6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "doc-extract", "language": "python", "name": "doc-extract"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}