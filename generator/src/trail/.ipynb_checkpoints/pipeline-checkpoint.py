import json
import os

from wpsai_insight_common.llm_gateway.models.chat_data import MultiModalType, MultiModalContent, SftMultiModalImage, \
    SftMultiModalText,  SftMultiModalImageUrl, Message

from wpsai_insight_common.llm_gateway.llm import LLModelRpc, LLMChatStatus
from wpsai_insight_common.llm_gateway.models.public_model_gateway import PublicModelGateway
from wpsai_insight_common.llm_gateway.models.sft_model_gateway import SftModelGateway
import openpyxl
import time
from openai import OpenAI


def render_vl_prompt(prefix, suffix, schema, page_content, url_content):
    content = []
    text_content = ""
    for i, url in enumerate(url_content):
        # 放入图片
        content.append(MultiModalContent(type=MultiModalType.image_url, content=url))
        text_content += page_content[i] + "\n\n"

    prompt = prefix + suffix.format(context=text_content, schema=schema)
    content.append(MultiModalContent(type=MultiModalType.text, content=prompt))
    messages = [Message(role="user", content=content)]
    return messages



class LMModel(object):
    gateway = LLModelRpc.Gateway.Public
    selector: LLModelRpc.ModelSelector = None

    @classmethod
    def generate_response_mllm(
            cls, messages, top_k: float = 0.99, temperature: float = 0.01
    ) -> (LLMChatStatus, str):


        status, text =  LLModelRpc().multimodal(cls.gateway, messages=messages, selector=cls.selector, top_k = top_k, temperature = temperature)
        if status != LLMChatStatus.OK:
            return status, ""


        return LLMChatStatus.OK, text
    
    @classmethod
    def generate_response(
            cls, messages: str = None, top_k: float = 0.99, temperature: float = 0.01
    ) -> (LLMChatStatus, str):
        status, text = LLModelRpc().chat_text(cls.gateway,messages=messages, selector=cls.selector, top_k = top_k, temperature = temperature)
        if status != LLMChatStatus.OK:
            return status, ""

        return LLMChatStatus.OK, text

def gen(messages, is_vl_model=False):
    pub_conf = PublicModelGateway.Conf(
        host="http://aigc-gateway-test.ksord.com",
        token="I26OQDZMT3pj2K3IhcRsUhRnlpG7PW4F",
        uid="9047",
        product_name="wps-kanmail-qa",
        intention_code="aigctest",
        provider="zhipu-zone",
        model="chatglm-130b-wps-128k",
        version=None,
        multimodal_provider="ali",
        multimodal_model="qwen-vl-max-0809",
        multimodal_version=None,
        sec_from="AI_DRIVE_KNOWLEDGE",
    )
    sft_conf = SftModelGateway.Conf(
        host="http://kmd-api.kas.wps.cn",
        multimodal_host="http://kmd-api.kas.wps.cn/api",
        token = 'sk-e3fb5ed3b4c342c980d2b30d0dd920c8'
    )
    LLModelRpc().create_models(pub_conf, sft_conf, None, pool_max=10)
    llm_model = {
            "gpt-4o": {
                "model": "gpt-4o",
                "provider": "azure",
                "version":"2024-05-13"
            },
            "qwen-max": {
                "model": "qwen-max",
                "provider": "ali",
                "version":"",
            },
            "gemini-2.5-pro-preview-05-06": {
                "model": "gemini-2.5-pro-preview-05-06",
                "provider": "google",
                "version":""
            },
            "qwen-vl-max": {
                "model": "qwen-vl-max-0809",
                "provider": "ali",
                "version":""
            },
            "o3": {
                "model": "o3",
                "provider": "azure",
                "version":"2025-04-16"
            }

    }
    

    if is_vl_model:
        model = "qwen-vl-max"
        LMModel.gateway = LLModelRpc.Gateway.Public
        LMModel.selector = LLModelRpc.ModelSelector(
            **llm_model[model])
        status, text = LMModel.generate_response_mllm(messages)
    else:
        model="qwen-max"
        LMModel.gateway = LLModelRpc.Gateway.Public
        LMModel.selector = LLModelRpc.ModelSelector(
            **llm_model[model])
        
        status, text = LMModel.generate_response(messages)
    return status, text


# 拿到dst结果
file_name = "kailaiying.json"
trail_file_name = "test-SAE报告-首次-扫描.pdf"


def get_content_infos(file_name):
    file_infos = {}
    with open(file_name, "r",encoding='utf-8') as f:
        data = json.load(f)

        for k, v in data.items():            
            images = v['Images']
            pages = v['Pages']
            page_mapping = {}

            for i in range(len(images)):
                page_mapping[i]['image_url'] = [m['url'] for m in images if m['page_num']==i][0]
                page_mapping[i]['page_content'] = [m['content'] for m in pages if m['page_num']==i]

            file_infos[k] = page_mapping

    return file_infos

def gen_json_schema(iamge_url, page_content):
    prefix = """你现在需要根据用户提供的图片和其OCR内容抽象出一个json，同时你还需要输出一个python函数，这个python函数需要实现将这个json对象重新转换为图片的OCR内容。

    该json需要包含图片的各种元素，例如标题，段落，表格，图片，复选框等。
    1. 表格信息需要包含跨行和跨列信息，rowspan，colspan。
    2. 复选框可能作为表格内容的一个cell也可能独立存在于文中，你还需要用字段来标识复选框的状态，checked=True|False。
    3. 有些部分是手写体的文字，你也需要用一个标识来进行标记，例如 handwriting=True|False。

    你的输出需要包含两部分：
    ```json
    这里输出你通过图片和OCR内容抽象出来的json对象
    ```

    ```python
    这里输出你将json对象重新转换为图片的OCR内容的python函数，输入参数为json_object, 输出对象为一个html，这个html的结构和内容需要尽可能和图片和原ocr内容保持一致。
    ```

    """


    suffix = f"""## OCR 内容
    {page_content}

请根据要求输出相应的json和python内容。
    """    

    content = []
    content.append(MultiModalContent(type=MultiModalType.image_url, content=iamge_url))
    content.append(MultiModalContent(type=MultiModalType.text, content=prefix+suffix))

    messages = [Message(role="user", content=content)]
    return messages

def pipeline(file_name):
    # 拿到dst结果
    """
    {
        "file_name": {
            0: {
                "image_url": "",
                "page_content": ""
            }
        }
    }
    """
    file_page_info_mapping = get_content_infos(file_name)

    for file, page_info in file_page_info_mapping.items():
        # todo delete
        if trail_file_name not in file:
            continue

        # 这里我们需要对拿到的图片信息和ocr数据去做数据框架的生成。
        for page_num, info in page_info.items():
            gen_json_schema(**info)


        # 拿到数据框架之后，将对应的数据框架保存。


    # 对每一个数据框架去生成数据




                
                
