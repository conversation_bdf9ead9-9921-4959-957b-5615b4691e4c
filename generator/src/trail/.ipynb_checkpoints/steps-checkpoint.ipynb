{"cells": [{"cell_type": "code", "execution_count": 25, "id": "bb1103bd-8499-4b8a-bab2-5af59a12bdc6", "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "\n", "from wpsai_insight_common.llm_gateway.models.chat_data import MultiModalType, MultiModalContent, SftMultiModalImage, \\\n", "    SftMultiModalText,  SftMultiModalImageUrl, Message\n", "\n", "from wpsai_insight_common.llm_gateway.llm import LLModelRpc, LLMChatStatus\n", "from wpsai_insight_common.llm_gateway.models.public_model_gateway import PublicModelGateway\n", "from wpsai_insight_common.llm_gateway.models.sft_model_gateway import SftModelGateway\n", "import openpyxl\n", "import time\n", "from openai import OpenAI\n", "\n", "\n", "def render_vl_prompt(prefix, suffix, schema, page_content, url_content):\n", "    content = []\n", "    text_content = \"\"\n", "    for i, url in enumerate(url_content):\n", "        # 放入图片\n", "        content.append(MultiModalContent(type=MultiModalType.image_url, content=url))\n", "        text_content += page_content[i] + \"\\n\\n\"\n", "\n", "    prompt = prefix + suffix.format(context=text_content, schema=schema)\n", "    content.append(MultiModalContent(type=MultiModalType.text, content=prompt))\n", "    messages = [Message(role=\"user\", content=content)]\n", "    return messages\n", "\n", "\n", "\n", "class LMModel(object):\n", "    gateway = LLModelRpc.Gateway.Public\n", "    selector: LLModelRpc.ModelSelector = None\n", "\n", "    @classmethod\n", "    def generate_response_mllm(\n", "            cls, messages, top_k: float = 0.99, temperature: float = 0.01, is_stream: bool = False\n", "    ) -> (LLMChatStatus, str):\n", "\n", "        status, text =  LLModelRpc().multimodal(cls.gateway, messages=messages, selector=cls.selector, top_k = top_k,\n", "                                                temperature = temperature, max_token=2000, is_stream=is_stream)\n", "        if status != LLMChatStatus.OK:\n", "            return status, \"\"\n", "\n", "\n", "        return LLMChatStatus.OK, text\n", "\n", "    @classmethod\n", "    async def async_generate_response_mllm(\n", "            cls, messages, top_k: float = 0.99, temperature: float = 0.01, is_stream: bool = False\n", "    ) -> (LLMChatStatus, str):\n", "        res = await LLModelRpc().async_chat_text_stream(LLModelRpc.Gateway.Public, cls, messages, top_k= 0.99, temperature = 0.01, max_token=20000)\n", "        final_res = \"\"\n", "        async for t in res:\n", "            final_res += t.text\n", "        return LLMChatStatus.OK, final_res\n", "\n", "    \n", "    \n", "    @classmethod\n", "    def generate_response(\n", "            cls, messages: str = None, top_k: float = 0.99, temperature: float = 0.01\n", "    ) -> (LLMChatStatus, str):\n", "        status, text = LLModelRpc().chat_text(cls.gateway,messages=messages, selector=cls.selector, top_k = top_k, temperature = temperature)\n", "        if status != LLMChatStatus.OK:\n", "            return status, \"\"\n", "\n", "        return LLMChatStatus.OK, text\n", "\n", "async def gen(messages, is_vl_model=False, is_stream=True):\n", "    pub_conf = PublicModelGateway.Conf(\n", "        host=\"http://ai-gateway.wps.cn\",\n", "        token=\"I26OQDZMT3pj2K3IhcRsUhRnlpG7PW4F\",\n", "        uid=\"9047\",\n", "        product_name=\"wps-kanmail-qa\",\n", "        intention_code=\"aigctest\",\n", "        provider=\"zhipu-zone\",\n", "        model=\"chatglm-130b-wps-128k\",\n", "        version=None,\n", "        multimodal_provider=\"ali\",\n", "        multimodal_model=\"qwen-vl-max-0809\",\n", "        multimodal_version=None,\n", "        sec_from=\"AI_DRIVE_KNOWLEDGE\",\n", "    )\n", "    sft_conf = SftModelGateway.Conf(\n", "        host=\"http://kmd-api.kas.wps.cn\",\n", "        multimodal_host=\"http://kmd-api.kas.wps.cn/api\",\n", "        token = 'sk-e3fb5ed3b4c342c980d2b30d0dd920c8'\n", "    )\n", "    LLModelRpc().create_models(pub_conf, sft_conf, None, pool_max=10)\n", "    llm_model = {\n", "        \"gpt-4o\": {\n", "            \"model\": \"gpt-4o\",\n", "            \"provider\": \"azure\",\n", "            \"version\":\"2024-05-13\"\n", "        },\n", "        \"qwen-max\": {\n", "            \"model\": \"qwen-max\",\n", "            \"provider\": \"ali\",\n", "            \"version\":\"\",\n", "        },\n", "        \"gemini-2.5-pro-preview-05-06\": {\n", "            \"model\": \"gemini-2.5-pro-preview-05-06\",\n", "            \"provider\": \"google\",\n", "            \"version\":\"\"\n", "        },\n", "        \"qwen-vl-max\": {\n", "            \"model\": \"qwen-vl-max-0809\",\n", "            \"provider\": \"ali\",\n", "            \"version\":\"\"\n", "        },\n", "        \"o3\": {\n", "            \"model\": \"o3\",\n", "            \"provider\": \"azure\",\n", "            \"version\":\"2025-04-16\"\n", "        },\n", "        \"claude-3-7-sonnet\": {\n", "            \"model\": \"claude-3-7-sonnet\",\n", "            \"provider\": \"aws\",\n", "            \"version\": \"20250219-v1:0\"\n", "        }\n", "    }\n", "    \n", "    \n", "    if is_vl_model:\n", "        \n", "        LMModel.gateway = LLModelRpc.Gateway.Public\n", "        LMModel.selector = LLModelRpc.ModelSelector(\n", "            **llm_model[model])\n", "        if is_stream:\n", "            status, text =await LMModel.async_generate_response_mllm \n", "        else:\n", "            status, text = LMModel.generate_response_mllm(messages)\n", "    else:\n", "        \n", "        LMModel.gateway = LLModelRpc.Gateway.Public\n", "        LMModel.selector = LLModelRpc.ModelSelector(\n", "            **llm_model[model])\n", "        \n", "        status, text = LMModel.generate_response(messages)\n", "    return status, text\n", "\n", "\n", "# 拿到dst结果\n", "file_name = \"kailaiying.json\"\n", "trail_file_name = \"test-SAE报告-首次-扫描.pdf\"\n", "\n", "\n", "def get_content_infos(file_name):\n", "    file_infos = {}\n", "    with open(file_name, \"r\",encoding='utf-8') as f:\n", "        data = json.load(f)\n", "\n", "        for k, v in data.items():\n", "            v = v['ocr_results']\n", "            print(v.keys())\n", "            images = v['Image']\n", "            pages = v['Pages']\n", "            page_mapping = {}\n", "\n", "            for i in range(len(images)):\n", "                if i not in page_mapping:\n", "                    page_mapping[i] = {}\n", "                page_mapping[i]['image_url'] = [m['url'] for m in images if m['page_num']==i][0]\n", "                page_mapping[i]['page_content'] = [m['content'] for m in pages if m['page_num']==i]\n", "\n", "            file_infos[k] = page_mapping\n", "\n", "    return file_infos\n", "\n", "def gen_json_schema(image_url, page_content):\n", "    prefix = \"\"\"你现在需要根据用户提供的图片和其OCR内容抽象出一个json，同时你还需要输出一个python函数，这个python函数需要实现将这个json对象重新转换为图片的OCR内容。\n", "\n", "    该json需要包含图片的各种元素，例如标题，段落，表格，图片，复选框等。\n", "    1. 表格信息需要包含跨行和跨列信息，rowspan，colspan。\n", "    2. 复选框可能作为表格内容的一个cell也可能独立存在于文中，你还需要用字段来标识复选框的状态，checked=True|False。\n", "    3. 有些部分是手写体的文字，你也需要用一个标识来进行标记，例如 handwriting=True|False。\n", "\n", "    你的输出需要包含两部分：\n", "    ```json\n", "    这里输出你通过图片和OCR内容抽象出来的json对象\n", "    ```\n", "\n", "    ```python\n", "    这里输出你将json对象重新转换为图片的OCR内容的python函数，输入参数为json_object, 输出对象为一个html，这个html的结构和内容需要尽可能和图片和原ocr内容保持一致。\n", "    ```\n", "\n", "    \"\"\"\n", "\n", "\n", "    suffix = f\"\"\"## OCR 内容\n", "    {page_content}\n", "\n", "请根据要求输出相应的json和python内容。\n", "    \"\"\"    \n", "\n", "    content = []\n", "    content.append(MultiModalContent(type=MultiModalType.image_url, content=image_url))\n", "    content.append(MultiModalContent(type=MultiModalType.text, content=prefix+suffix))\n", "\n", "    # content.append(MultiModalContent(type=MultiModalType.text, content=\"你好\"))\n", "\n", "    messages = [Message(role=\"user\", content=content)]\n", "\n", "    print(messages)\n", "\n", "    status, text = gen(messages=messages, is_vl_model=True, is_stream=True)\n", "\n", "    print(status, text)\n", "    return status, text\n", "\n", "def pipeline(file_name):\n", "    # 拿到dst结果\n", "    \"\"\"\n", "    {\n", "        \"file_name\": {\n", "            0: {\n", "                \"image_url\": \"\",\n", "                \"page_content\": \"\"\n", "            }\n", "        }\n", "    }\n", "    \"\"\"\n", "    file_page_info_mapping = get_content_infos(file_name)\n", "\n", "    for file, page_info in file_page_info_mapping.items():\n", "        # todo delete\n", "        if trail_file_name not in file:\n", "            continue\n", "\n", "        # 这里我们需要对拿到的图片信息和ocr数据去做数据框架的生成。\n", "        for page_num, info in page_info.items():\n", "            gen_json_schema(**info)\n", "            break\n", "        break\n", "\n", "\n", "        # 拿到数据框架之后，将对应的数据框架保存。\n", "\n", "\n", "    # 对每一个数据框架去生成数据\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 26, "id": "1bf9beae-5a44-411a-9393-abcaf9d42d2e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["ERROR:asyncio:Unclosed client session\n", "client_session: <aiohttp.client.ClientSession object at 0x000001D22652DC00>\n", "WARNING:root:Tracer 尚未初始化，返回 NoOpTracer。\n"]}, {"name": "stdout", "output_type": "stream", "text": ["dict_keys(['Chun<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', 'IsImage', 'ReqType', 'Pages'])\n", "dict_keys(['Chun<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', 'IsImage', 'ReqType', 'Pages'])\n", "dict_keys(['Chun<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', 'IsImage', 'ReqType', 'Pages'])\n", "dict_keys(['Chun<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', 'IsImage', 'ReqType', 'Pages'])\n", "dict_keys(['Chun<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', 'IsImage', 'ReqType', 'Pages'])\n", "dict_keys(['Chun<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', 'IsImage', 'ReqType', 'Pages'])\n", "[Message(content=[MultiModalContent(type=<MultiModalType.image_url: 'image_url'>, content='http://kna-model.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/88bbb5c9be8d432489d9afa7e24aa4f2.png?Signature=5m470tXT3RWo%2Bjc5DotwfSi0vrU%3D&Expires=**********&KSSAccessKeyId=AKLT3OWV80e4QWe6KQ5x8Viz'), MultiModalContent(type=<MultiModalType.text: 'text'>, content='你现在需要根据用户提供的图片和其OCR内容抽象出一个json，同时你还需要输出一个python函数，这个python函数需要实现将这个json对象重新转换为图片的OCR内容。\\n\\n    该json需要包含图片的各种元素，例如标题，段落，表格，图片，复选框等。\\n    1. 表格信息需要包含跨行和跨列信息，rowspan，colspan。\\n    2. 复选框可能作为表格内容的一个cell也可能独立存在于文中，你还需要用字段来标识复选框的状态，checked=True|False。\\n    3. 有些部分是手写体的文字，你也需要用一个标识来进行标记，例如 handwriting=True|False。\\n\\n    你的输出需要包含两部分：\\n    ```json\\n    这里输出你通过图片和OCR内容抽象出来的json对象\\n    ```\\n\\n    ```python\\n    这里输出你将json对象重新转换为图片的OCR内容的python函数，输入参数为json_object, 输出对象为一个html，这个html的结构和内容需要尽可能和图片和原ocr内容保持一致。\\n    ```\\n\\n    ## OCR 内容\\n    [\\'严重不良事件报告表(SAE)\\\\n    新药临床批准文号：\\\\n\\\\n    报告类型：\\\\n\\\\n    冈首次\\\\n\\\\n    □随访□总结\\\\n\\\\n    报告时间：2023年10月30日\\\\n\\\\n    <table><tr><td rowspan=\"1\" colspan=\"4\">临床项目及报告单位信息</td></tr><tr><td rowspan=\"1\" colspan=\"1\">医疗机构及专业名称</td><td rowspan=\"1\" colspan=\"1\">测试医院骨髓移植科</td><td rowspan=\"1\" colspan=\"1\">电话</td><td rowspan=\"1\" colspan=\"1\">022-12345675</td></tr><tr><td rowspan=\"1\" colspan=\"1\">申报单位名称</td><td rowspan=\"1\" colspan=\"1\">凯诺医药</td><td rowspan=\"1\" colspan=\"1\">电话</td><td rowspan=\"1\" colspan=\"1\">+86-12345678</td></tr><tr><td rowspan=\"1\" colspan=\"1\">临床研究方案名称</td><td rowspan=\"1\" colspan=\"3\">测试试验药品治疗肿瘤的临床试验方案</td></tr><tr><td rowspan=\"1\" colspan=\"1\">临床研究方案号</td><td rowspan=\"1\" colspan=\"3\">Clin-001</td></tr><tr><td rowspan=\"1\" colspan=\"1\">临床适应症</td><td rowspan=\"1\" colspan=\"3\">肿瘤</td></tr><tr><td rowspan=\"1\" colspan=\"1\">临床研究分类</td><td rowspan=\"1\" colspan=\"3\">□Ⅲ期□Ⅱ期区I期口临床验□生物等效性试验□IV期证类试验</td></tr><tr><td rowspan=\"1\" colspan=\"1\">试验盲态情况</td><td rowspan=\"1\" colspan=\"3\">年 月  日)口盲态(□未破盲□已盲态-破盲时间：区非盲态</td></tr><tr><td rowspan=\"1\" colspan=\"4\">报告者信息</td></tr><tr><td rowspan=\"1\" colspan=\"1\">报告者姓名</td><td rowspan=\"1\" colspan=\"1\">张三</td><td rowspan=\"1\" colspan=\"1\">所在国家</td><td rowspan=\"1\" colspan=\"1\">中国</td></tr><tr><td rowspan=\"1\" colspan=\"1\">职业</td><td rowspan=\"1\" colspan=\"1\">医生</td><td rowspan=\"1\" colspan=\"1\">电话</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">获知SAE时间</td><td rowspan=\"1\" colspan=\"3\">区首次获知时间2023年  10月30日□随访信息获知时间___年_____月__日</td></tr><tr><td rowspan=\"1\" colspan=\"4\">受试者信息</td></tr></table>\\\\n\\\\n\\']\\n\\n请根据要求输出相应的json和python内容。\\n    ')], role='user', name=None, tool_calls=None)]\n", "jdata\n", " {'stream': False, 'provider': 'ali', 'model': 'qwen-vl-max-0809', 'version': '', 'messages': [{'content': [{'type': <MultiModalType.image_url: 'image_url'>, 'content': 'http://kna-model.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/88bbb5c9be8d432489d9afa7e24aa4f2.png?Signature=5m470tXT3RWo%2Bjc5DotwfSi0vrU%3D&Expires=**********&KSSAccessKeyId=AKLT3OWV80e4QWe6KQ5x8Viz'}, {'type': <MultiModalType.text: 'text'>, 'content': '你现在需要根据用户提供的图片和其OCR内容抽象出一个json，同时你还需要输出一个python函数，这个python函数需要实现将这个json对象重新转换为图片的OCR内容。\\n\\n    该json需要包含图片的各种元素，例如标题，段落，表格，图片，复选框等。\\n    1. 表格信息需要包含跨行和跨列信息，rowspan，colspan。\\n    2. 复选框可能作为表格内容的一个cell也可能独立存在于文中，你还需要用字段来标识复选框的状态，checked=True|False。\\n    3. 有些部分是手写体的文字，你也需要用一个标识来进行标记，例如 handwriting=True|False。\\n\\n    你的输出需要包含两部分：\\n    ```json\\n    这里输出你通过图片和OCR内容抽象出来的json对象\\n    ```\\n\\n    ```python\\n    这里输出你将json对象重新转换为图片的OCR内容的python函数，输入参数为json_object, 输出对象为一个html，这个html的结构和内容需要尽可能和图片和原ocr内容保持一致。\\n    ```\\n\\n    ## OCR 内容\\n    [\\'严重不良事件报告表(SAE)\\\\n    新药临床批准文号：\\\\n\\\\n    报告类型：\\\\n\\\\n    冈首次\\\\n\\\\n    □随访□总结\\\\n\\\\n    报告时间：2023年10月30日\\\\n\\\\n    <table><tr><td rowspan=\"1\" colspan=\"4\">临床项目及报告单位信息</td></tr><tr><td rowspan=\"1\" colspan=\"1\">医疗机构及专业名称</td><td rowspan=\"1\" colspan=\"1\">测试医院骨髓移植科</td><td rowspan=\"1\" colspan=\"1\">电话</td><td rowspan=\"1\" colspan=\"1\">022-12345675</td></tr><tr><td rowspan=\"1\" colspan=\"1\">申报单位名称</td><td rowspan=\"1\" colspan=\"1\">凯诺医药</td><td rowspan=\"1\" colspan=\"1\">电话</td><td rowspan=\"1\" colspan=\"1\">+86-12345678</td></tr><tr><td rowspan=\"1\" colspan=\"1\">临床研究方案名称</td><td rowspan=\"1\" colspan=\"3\">测试试验药品治疗肿瘤的临床试验方案</td></tr><tr><td rowspan=\"1\" colspan=\"1\">临床研究方案号</td><td rowspan=\"1\" colspan=\"3\">Clin-001</td></tr><tr><td rowspan=\"1\" colspan=\"1\">临床适应症</td><td rowspan=\"1\" colspan=\"3\">肿瘤</td></tr><tr><td rowspan=\"1\" colspan=\"1\">临床研究分类</td><td rowspan=\"1\" colspan=\"3\">□Ⅲ期□Ⅱ期区I期口临床验□生物等效性试验□IV期证类试验</td></tr><tr><td rowspan=\"1\" colspan=\"1\">试验盲态情况</td><td rowspan=\"1\" colspan=\"3\">年 月  日)口盲态(□未破盲□已盲态-破盲时间：区非盲态</td></tr><tr><td rowspan=\"1\" colspan=\"4\">报告者信息</td></tr><tr><td rowspan=\"1\" colspan=\"1\">报告者姓名</td><td rowspan=\"1\" colspan=\"1\">张三</td><td rowspan=\"1\" colspan=\"1\">所在国家</td><td rowspan=\"1\" colspan=\"1\">中国</td></tr><tr><td rowspan=\"1\" colspan=\"1\">职业</td><td rowspan=\"1\" colspan=\"1\">医生</td><td rowspan=\"1\" colspan=\"1\">电话</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">获知SAE时间</td><td rowspan=\"1\" colspan=\"3\">区首次获知时间2023年  10月30日□随访信息获知时间___年_____月__日</td></tr><tr><td rowspan=\"1\" colspan=\"4\">受试者信息</td></tr></table>\\\\n\\\\n\\']\\n\\n请根据要求输出相应的json和python内容。\\n    '}], 'role': 'user'}], 'base_llm_arguments': {'temperature': 0.01, 'max_tokens': 2000}, 'sec_text': {'answer_flag': 0}}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:ai-gateway fail, url: http://ai-gateway.wps.cn/api/v2/llm/multimodal, status_code: 504, res_text: <html>\n", "<head><title>504 Gateway Time-out</title></head>\n", "<body>\n", "<center><h1>504 Gateway Time-out</h1></center>\n", "<hr><center>ZLB/********.2</center>\n", "</body>\n", "</html>\n", "\n", "ERROR:root:1 validation error for ChatResponse\n", "__root__\n", "  Expecting value: line 1 column 1 (char 0) [type=value_error.jsondecode, input_value='<html>\\r\\n<head><title>5...n</body>\\r\\n</html>\\r\\n', input_type=str]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LLMChatStatus.FAIL \n"]}], "source": ["model = \"claude-3-7-sonnet\"\n", "model = \"qwen-vl-max\"\n", "pipeline(file_name=file_name)"]}, {"cell_type": "code", "execution_count": null, "id": "fb467a54-b5e3-4993-ba39-95af2c4084f6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "doc-extract", "language": "python", "name": "doc-extract"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}