import jinja2
import json, os, sys
project_root = r"D:\kingsoft\document_gen"

# 添加到 sys.path
sys.path.append(project_root)
print(sys.path)
from src.converter.text2img import html2image
templates = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>A4 页面布局</title>
    <style>
        body {
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            padding: 10mm;
            box-sizing: border-box;
            font-family: sans-serif;
            background-color: #f9f9f9;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }

        h1 {
            text-align: center;
        }

        /* 打印样式 */
        @media print {
            body {
                margin: 0;
                padding: 0;
                background-color: white;
                -webkit-print-color-adjust: exact;
            }
        }

        .inner-wireless {
            border-collapse: collapse;
            border: 1px solid black;
            width: 100%; /* 或其他宽度 */
        }

        .inner-wireless th {
            border: none;
            padding: 8px; /* 可选的内边距 */
            text-align: center; /* 可选的文本对齐 */
        }
        .inner-wireless td {
            border: none;
            padding: 8px; /* 可选的内边距 */
            text-align: left; /* 可选的文本对齐 */
        }

        .wireless {
            border-collapse: collapse;
            width: 100%; /* 或其他宽度 */
        }

        .wireless th {
            border: none;
            padding: 8px; /* 可选的内边距 */
            text-align: center; /* 可选的文本对齐 */
        }
        
        .wireless td {
            border: none;
            padding: 8px; /* 可选的内边距 */
            text-align: left; /* 可选的文本对齐 */
        }

    </style>
</head>

<body>
{{body}}
</body>
</html>
"""

tb_mapping = {
    "wire": '<table border="1">',
    "wireless": '<table class="wireless">',
    "inner-wireless": '<table class="inner-wireless">'
}

def convert_json_to_html(json_object):

    elements = json_object.get('elements', [])

    html_parts = []

    for element in elements:

        type_ = element.get('type')

        if type_ == 'title':

            level = element.get('level', 1)
            content = element.get('content', '')
            html_parts.append(f'<h{level}>{content}</h{level}>')

        elif type_ == 'paragraph':

            content = element.get('content', '')

            html_parts.append(f'<p>{content}</p>')


        elif type_ == 'checkbox':
            key = element.get('key', '')
            options = element.get('options', [])
            checkbox_html = f'<div><strong>{key}:</strong>'

            for option in options:
                option_text = option.get('option', '')
                checked = 'checked' if option.get('checked') else ''
                checkbox_html += f'<label><input type="checkbox" {checked}> {option_text}</label>'

            checkbox_html += '</div>'
            html_parts.append(checkbox_html)

        elif type_ == 'list':

            items = element.get('items', [])

            list_html = '<ul>'
            for item in items:

                list_html += f'<li>{item}</li>'

            list_html += '</ul>'
            html_parts.append(list_html)

        elif type_ == 'table':
            rows = element.get('rows', [])
            style = element.get('style', "wire")
            table_html = tb_mapping[style]


            for i, row in enumerate(rows):
                open_mark = "td" if i != 0 else "th"
                
                table_html += f"<tr>"

                for cell in row:
                    content = cell.get('content', '')
                    rowspan = cell.get('rowspan', 1)
                    colspan = cell.get('colspan', 1)
                    cell_content = ""


                    if isinstance(content, str):
                        cell_content = content

                    elif isinstance(content, dict) and 'options' in content:
                        # 检查pre，content是否包含key
                        if content['key'] not in table_html:
                            table_html += f'<{open_mark} rowspan="1" colspan="1">{content["key"]}</{open_mark}>'

                        options = content.get('options', [])
                        for option in options:
                            option_text = option.get('option', '')
                            checked = 'checked' if option.get('checked') else ''
                            cell_content += f'<label><input type="checkbox" {checked}> {option_text}</label>'


                    elif isinstance(content, list):

                        cell_content += '<ul>'

                        for item in content:

                            cell_content += f'<li>{item}</li>'

                        cell_content += '</ul>'


                    table_html += f'<{open_mark} rowspan="{rowspan}" colspan="{colspan}">{cell_content}</{open_mark}>'

                
                table_html += f'</tr>'
            table_html += '</table>'

            html_parts.append(table_html)

        elif type_ == 'signature':

            content = element.get('content', '')

            html_parts.append(f'<div><em>Signature: {content}</em></div>')


        elif type_ == 'date':
            content = element.get('content', '')
            html_parts.append(f'<div><em>Date: {content}</em></div>')
    print(html_parts)
    return ''.join(html_parts)


if __name__ == "__main__":
    # 获取项目根目录

    example_data = json.load(open(r"D:\kingsoft\document_gen\src\trail\sample.json", "r", encoding="utf-8"))

    print(example_data)
    
    body = convert_json_to_html(example_data)
    html = jinja2.Template(templates).render(body=body)
    print(html)
    img_path = html2image(html, r"D:\kingsoft\document_gen\src\trail\images")
    print(img_path)