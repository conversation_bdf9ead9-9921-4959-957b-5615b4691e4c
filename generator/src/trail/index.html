<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>A4 页面布局</title>
        <style>
                body {
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            padding: 10mm;
            box-sizing: border-box;
            font-family: sans-serif;
            background-color: #f9f9f9;
        }

        table {
            width: 100%;
            margin-top: 20px;
        }

        th, td {          
            padding: 8px;
            text-align: center;
        } 

        h1 {
            text-align: center;
        }
        
        /* 有线无线表 */
        .wire_wireless {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border: 1px solid;
            text-align: left;
        }

        .wire_wireless td {
            border: 1px solid;
        }

        .wire_wireless tr:nth-child(n + 3) td {
            border: none;
        }

        /* 三线表 */
        .three_line {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;            
        }

        .three_line tr {
            &:first-child{
                border-bottom: 1px solid;
                border-top:   1px solid ;
            }
          
            &:last-child{
                border-bottom: 1px solid;
            }
        }

        /* 仅外表框的表 */
        .border_only {
            border: 1px solid;
        }

        /* 无线表 */
        .wireless {
            border: none;
            text-align: center;
        }

        /* 有线表 */
        .wire {
            border: 0px;
            width:100%;
            border-collapse: collapse;
        }

        .wire td{
            border: 1px solid;
            text-align: center;
        }

        .invoice {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            text-align: left;
        }

        .invoice tr:nth-child(1) td:nth-last-child(-n+3) {
            border-bottom: 1px solid black;
        }

        .invoice tr span {
            border-bottom: 1px solid black;
        }

        /* 打印样式 */
        @media print {
            body {
                margin: 0;
                padding: 0;
                background-color: white;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>

<body>
  <table class="invoice"><caption>测试</caption><tr><td rowspan="1" colspan="1">类别</td><td rowspan="1" colspan="1">调整前</td><td rowspan="1" colspan="1">调整金额</td><td rowspan="1" colspan="1">调整后</td></tr><tr><td rowspan="1" colspan="4">资产</td></tr><tr><td rowspan="1" colspan="1">以公允价值计量且其变动计入当期损益的金融投资</td><td rowspan="1" colspan="1"><span>705,357</span></td><td rowspan="1" colspan="1"><span>688</span></td><td rowspan="1" colspan="1"><span>706,045</span></td></tr><tr><td rowspan="1" colspan="1">以摊余成本计量的金融投资</td><td rowspan="1" colspan="1"><span>2,450,775</span></td><td rowspan="1" colspan="1"><span>(19,151)</span></td><td rowspan="1" colspan="1"><span>2,431,624</span></td></tr><tr><td rowspan="1" colspan="1">以公允价值计量且其变动计入其他综合收益的金融投资</td><td rowspan="1" colspan="1">799,075</td><td rowspan="1" colspan="1">18,971</td><td rowspan="1" colspan="1">818,046</td></tr><tr><td rowspan="1" colspan="4">股东权益</td></tr><tr><td rowspan="1" colspan="1">其他综合收益</td><td rowspan="1" colspan="1"><span>(4,069)</span></td><td rowspan="1" colspan="1"><span>191</span></td><td rowspan="1" colspan="1"><span>(3,878)</span></td></tr><tr><td rowspan="1" colspan="1">未分配利润</td><td rowspan="1" colspan="1">292,734</td><td rowspan="1" colspan="1">127</td><td rowspan="1" colspan="1">292,861</td></tr><tr><td rowspan="1" colspan="1">少数股东权益</td><td rowspan="1" colspan="1">8,040</td><td rowspan="1" colspan="1">190</td><td rowspan="1" colspan="1">8,230</td></tr></table>
  </body>
  </html>