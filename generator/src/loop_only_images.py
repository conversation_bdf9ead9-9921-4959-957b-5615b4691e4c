from image_gen import gen_image_data, gen_complex_image_data
from llm_train_data_gen import gen_short_long_data
from datetime import date
import os
import time
from converter.image_noise_v1 import batch_process_images
import json

data_path = os.getenv("GENERATOR_DATA_DIR", "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/data")

read_list = []
sleep_second = 10 
complexd = True

for i in range(10000000000):
    current_date = date.today()

    data_dir_list = os.listdir(data_path)

    for data_dir in data_dir_list:        
        file_path = os.path.join(data_path, data_dir)
        
        if data_dir not in read_list and not os.path.exists(os.path.join(file_path, "images")) and os.path.exists(os.path.join(file_path, "llm_gen_data.json")):
            # 生成图片数据
            print("开始生成图片")
            if complexd:
                gen_complex_image_data(file_path, max_workers=200)
            else:
                gen_image_data(file_path)
            print("开始生成大模型训练数据")
            # 生成大模型训练数据
            
           
            print("生成结束，sleep 1 hour")
            read_list.append(data_dir)



            # time.sleep(60 * 60 * 1)
            

    print(f"sleep {sleep_second} seconds")
    time.sleep(sleep_second)


    


    