from image_gen import gen_image_data, gen_complex_image_data
from llm_train_data_gen import gen_short_long_data
from datetime import date
import os
import time
from converter.image_noise_v1 import batch_process_images
import json
import random

data_path = os.getenv("GENERATOR_DATA_DIR", "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/data")

read_list = [
    "2025-06-19_2000",
    "2025-06-19_4000",
    "2025-06-19_6000",
    "2025-06-19_8000",
    "2025-06-19_10000",
    "2025-06-19_12000",
    "2025-06-19_14000"
    ]
sleep_second = 10 
complexd = True
complex_type = "MARK"
complex_type_list = ["mark",  "left", "miss", "all"]

for i in range(10000000000):
    current_date = date.today()

    data_dir_list = os.listdir(data_path)

    for data_dir in data_dir_list:        
        if data_dir in read_list:
            continue
        for complex_type in complex_type_list:
            file_path = os.path.join(data_path, data_dir)

            try:
            
                if os.path.exists(os.path.join(file_path, "llm_gen_data.json")):
                    print(file_path)
                    # 生成图片数据
                    # print("开始生成图片")
                    # if complexd:
                    #     gen_complex_image_data(file_path, max_workers=200)
                    # else:
                    #     gen_image_data(file_path)
                    print("开始修改图片路径")
                    data_file_path = os.path.join(file_path,  f"all_train_data_{complex_type}_0721.json")
                    new_file_path = os.path.join(file_path, f"all_train_data_{complex_type}_local_0721.json")
                    # t_type = ["test", "train"]
                    # l_type = ['short', 'long']

                    # for t in t_type:
                    #     for l in l_type:
                    #         data_file_path = os.path.join(file_path,  f"llm_data_{t}_{l}.json")
                    #         new_file_path = os.path.join(file_path,  f"llm_data_{t}_{l}.json")
                    #         with open(data_file_path) as f:
                    #             data = json.load(f)

                    #         for i, d in enumerate(data):
                    #             data[i]['images'] = [d['images'][0].replace(old_path, new_path)]
                                                                                                        
                    #         with open(new_file_path, "w") as f:
                    #             json.dump(data, f, ensure_ascii=False, indent=2)



                    # data_file_path = os.path.join(file_path)

                    with open(data_file_path) as f:
                        data = json.load(f)

                    for i, d in enumerate(data):
                        old_path = "/home/<USER>/kas_cache/pengying/llm_checkbox_gen/data"
                        new_path = os.getenv("GENERATOR_DATA_DIR", "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/data")
                        data[i]['images'] = [d['images'][0].replace(old_path, new_path)]
                                                                                                
                    with open(new_file_path, "w") as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)

                    read_list.append(data_dir)            
            except:
                print("continue")        


    print(f"sleep {sleep_second} seconds")
    time.sleep(sleep_second)


    


    