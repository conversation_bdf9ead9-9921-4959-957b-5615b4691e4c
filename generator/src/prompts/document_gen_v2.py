import json
from prompts.schema_proposal import schema_proposal
# 新版本的数据，更加复杂
json_schema_for_doc_gen_v2 = {
    "type": "object",
    "properties": {
        "elements": {
            "type": "array",
            "items": {
                "oneOf": [
                    schema_proposal['title_schema'],
                    schema_proposal['paragraph_schema'],
                    schema_proposal['checkbox_schema'],
                    schema_proposal['list_schema'],
                    schema_proposal['table_schema'],
                    schema_proposal['signature_schema'],
                    schema_proposal['date_schema'],
                ]         
            }
        }
    },
    "definitions": {
        "checkbox": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string"
                },
                "options": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "option": {
                                "type": "string"
                            },
                            "checked": {
                                "type": "boolean"
                            }
                        }
                    }
                }
            }
        },
        "list": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        }
    },
    "required": [
        "elements"
    ]
}


json_schema_for_doc_gen = {
    "type": "object",
    "properties": {
        "elements": {
            "type": "array",
            "items": {
                "oneOf": [
                    {
                        "type": "object",
                        "properties": {
                            "type": {
                                "enum": [
                                    "title"
                                ]
                            },
                            "level": {
                                "type": "integer",
                                "minimum": 1
                            },
                            "content": {
                                "type": "string"
                            }
                        },
                        "required": [
                            "type",
                            "level",
                            "content"
                        ]
                    },
                    {
                        "type": "object",
                        "properties": {
                            "type": {
                                "enum": [
                                    "paragraph"
                                ]
                            },
                            "content": {
                                "type": "string"
                            }
                        },
                        "required": [
                            "type",
                            "content"
                        ]
                    },
                    {
                        "type": "object",
                        "properties": {
                            "type": {
                                "enum": [
                                    "checkbox"
                                ]
                            },
                            "key": {
                                "type": "string"
                            },
                            "options": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "option": {
                                            "type": "string"
                                        },
                                        "checked": {
                                            "type": "boolean"
                                        }
                                    },
                                    "required": [
                                        "option",
                                        "checked"
                                    ]
                                }
                            }
                        },
                        "required": [
                            "type",
                            "key",
                            "options"
                        ]
                    },
                    {
                        "type": "object",
                        "properties": {
                            "type": {
                                "enum": [
                                    "list"
                                ]
                            },
                            "items": {
                                "type": "array",
                                "items": {
                                    "type": "string"
                                }
                            }
                        },
                        "required": [
                            "type",
                            "items"
                        ]
                    },
                    {
                        "type": "object",
                        "properties": {
                            "type": {
                                "enum": [
                                    "table"
                                ]
                            },
                            "rows": {
                                "type": "array",
                                "items": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "content": {
                                                "oneOf": [
                                                    {
                                                        "type": "string"
                                                    },
                                                    {
                                                        "type": "object",
                                                        "$ref": "#/definitions/checkbox"
                                                    },
                                                    {
                                                        "type": "object",
                                                        "$ref": "#/definitions/list"
                                                    }
                                                ]
                                            },
                                            "rowspan": {
                                                "type": "integer",
                                                "minimum": 1
                                            },
                                            "colspan": {
                                                "type": "integer",
                                                "minimum": 1
                                            }
                                        },
                                        "required": [
                                            "content"
                                        ]
                                    }
                                }
                            }
                        },
                        "required": [
                            "type",
                            "rows"
                        ]
                    },
                    {
                        "type": "object",
                        "properties": {
                            "type": {
                                "enum": [
                                    "signature"
                                ]
                            },
                            "content": {
                                "type": "string"
                            }
                        },
                        "required": [
                            "type",
                            "content"
                        ]
                    },
                    {
                        "type": "object",
                        "properties": {
                            "type": {
                                "enum": [
                                    "date"
                                ]
                            },
                            "content": {
                                "type": "string",
                                "format": "date"
                            }
                        },
                        "required": [
                            "type",
                            "content"
                        ]
                    }
                ]
            }
        }
    },
    "definitions": {
        "checkbox": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string"
                },
                "options": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "option": {
                                "type": "string"
                            },
                            "checked": {
                                "type": "boolean"
                            }
                        }
                    }
                }
            }
        },
        "list": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        }
    },
    "required": [
        "elements"
    ]
}

example = json.dumps({
    "elements": [
      {
        "type": "title",
        "level": 1,
        "content": "严重不良事件报告表(SAE)"
      },
      {
        "type": "paragraph",
        "content": "新药临床批准文号："
      },
      {
        "type": "checkbox",
        "key": "报告类型",
        "options": [
          {
            "option": "首次",
            "checked": True
          },
          {
            "option": "随访",
            "checked": False
          },
          {
            "option": "总结",
            "checked": False
          }
        ]
      },
      {
        "type": "date",
        "content": "2023-10-30"
      },
      {
        "type": "table",
        "style": "wire",
        "caption": "表1：临床检测报告",
        "rows": [
          [
            {
              "content": "临床项目及报告单位信息",
              "colspan": 4
            }
          ],
          [
            {
              "content": "医疗机构及专业名称"
            },
            {
              "content": "测试医院骨髓移植科"
            },
            {
              "content": "电话"
            },
            {
              "content": "022-12345675"
            }
          ],
          [
            {
              "content": "申报单位名称"
            },
            {
              "content": "凯诺医药"
            },
            {
              "content": "电话"
            },
            {
              "content": "+86-12345678"
            }
          ],
          [
            {
              "content": "临床研究方案名称"
            },
            {
              "content": "测试试验药品治疗肿瘤的临床试验方案",
              "colspan": 3
            }
          ],
          [
            {
              "content": "临床研究方案号"
            },
            {
              "content": "Clin-001",
              "colspan": 3
            }
          ],
          [
            {
              "content": "临床适应症"
            },
            {
              "content": "肿瘤",
              "colspan": 3
            }
          ],
          [
            {
              "content": "临床研究分类"
            },
            {
              "content": {
                "key": "临床研究分类",
                "options": [
                  {
                    "option": "Ⅰ期",
                    "checked": True
                  },
                  {
                    "option": "Ⅱ期",
                    "checked": False
                  },
                  {
                    "option": "Ⅲ期",
                    "checked": False
                  },
                  {
                    "option": "IV期",
                    "checked": False
                  },
                  {
                    "option": "生物等效性试验",
                    "checked": False
                  },
                  {
                    "option": "证类试验",
                    "checked": False
                  }
                ]
              },
              "colspan": 3
            }
          ],
          [
            {
              "content": "试验盲态情况"
            },
            {
              "content": {
                "key": "试验盲态情况",
                "options": [
                  {
                    "option": "盲态",
                    "checked": False
                  },
                  {
                    "option": "未破盲",
                    "checked": False
                  },
                  {
                    "option": "已破盲",
                    "checked": False
                  },
                  {
                    "option": "非盲态",
                    "checked": True
                  }
                ]
              },
              "colspan": 3
            }
          ],
          [
            {
              "content": "报告者信息",
              "colspan": 4
            }
          ],
          [
            {
              "content": "报告者姓名"
            },
            {
              "content": "张三"
            },
            {
              "content": "所在国家"
            },
            {
              "content": "中国"
            }
          ],
          [
            {
              "content": "职业"
            },
            {
              "content": "医生"
            },
            {
              "content": "电话"
            },
            {
              "content": ""
            }
          ],
          [
            {
              "content": "获知SAE时间"
            },
            {
              "content": {
                "key": "获知SAE时间",
                "options": [
                  {
                    "option": "首次获知时间",
                    "checked": True
                  },
                  {
                    "option": "随访信息获知时间",
                    "checked": False
                  }
                ]
              },
              "colspan": 3
            }
          ],
          [
            {
              "content": "受试者信息",
              "colspan": 4
            }
          ]
        ]
      }
    ]
  }, ensure_ascii=False, indent=4)


# document_gen_prompt = """我需要生成{industry}行业下面的{topic}相关的文档。
#                 第一步：你需要想出该文档的一个主题，即标题包含这个文档的全部内容。
#                 第二步：你需要对该文档进行内容的填充，在填充过程中你需要尽可能地使用checkbox这个类型来丰富你的文档内容，checkbox需要包含checked=True和False这两种类型; 还有checkbox穿插在表格中， 在表格中穿插的checkbox一般前一个cell对应的内容为当前checkbox的key。
#                 第三步：除了checkbox，文字信息和表格信息也是很重要的元素，请使用这两种类型来丰富你的文档内容。
#                 第四步：最后一步，你需要将以上两步的内容整合起来形成一个完整的文档，并将其以markdown形式返回给用户。
                
                
#                 你的输出结果应该以Json格式展示，并遵从以下schema格式。                
#                 {schema}
                
#                 示例输出：
#                 ```json
#                 {example}
#                 ```
                
#                 请输出你的结果：
#                 ```json/nothink"""

document_gen_prompt = """我需要生成{industry}行业下面的{topic}相关的文档。
                第一步：你需要想出该文档的一个主题，即标题包含这个文档的全部内容。
                第二步：你需要对该文档进行内容的填充，在填充过程中你需要尽可能地使用table这个类型来丰富你的文档内容，table的类型从['wire-wireless','three-line', 'wireless', 'wire', 'border-only','invoice']中选择，一张图片中可以生成多种表格，也可以生成一种。
                第三步：每张表格不一定有caption，如果有caption，那么caption的内容要和表格的内容相关，且如果有多个表格，则表格的caption以 表1，表2等为开头。
                第四步：除了表格，文字信息和checkbox信息也是很重要的元素，请使用这两种类型来丰富你的文档内容。
                第五步：如果表格的类型为invoice，那么则尽可能以时间，事项名称和数字来组合内容。
                                
                你的输出结果应该以Json格式展示，并遵从以下schema格式。                
                {schema}
                
                示例输出：
                ```json
                {example}
                ```
                
                请输出你的结果：
                ```json/nothink"""


if __name__ == "__main__":
  pass

