schema_proposal = {
    "title_schema":{
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "title"
                ]
            },
            "level": {
                "type": "integer",
                "minimum": 1
            },
            "content": {
                "type": "string"
            }
        },
        "required": [
            "type",
            "level",
            "content"
        ]
    },
    "paragraph_schema": {
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "paragraph"
                ]
            },
            "content": {
                "type": "string"
            }
        },
        "required": [
            "type",
            "content"
        ]
    },
    "checkbox_schema": {
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "checkbox"
                ]
            },
            "key": {
                "type": "string"
            },
            "options": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "option": {
                            "type": "string"
                        },
                        "checked": {
                            "type": "boolean"
                        }
                    },
                    "required": [
                        "option",
                        "checked"
                    ]
                }
            }
        },
        "required": [
            "type",
            "key",
            "options"
        ]
    },
    "list_schema": {
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "list"
                ]
            },
            "items": {
                "type": "array",
                "items": {
                    "type": "string"
                }
            }
        },
        "required": [
            "type",
            "items"
        ]
    },
    "table_schema":  {
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "table"
                ]
            },
            "style": {
                "type": "string",
                "enum": ["wire-wireless","three-line", "wireless", "wire", "border-only","invoice"]
            },
            "caption": {
                "type": "string"
            },
            "rows": {
                "type": "array",
                "items": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "content": {
                                "oneOf": [
                                    {
                                        "type": "string"
                                    },
                                    {
                                        "type": "object",
                                        "$ref": "#/definitions/checkbox"
                                    },
                                    {
                                        "type": "object",
                                        "$ref": "#/definitions/list"
                                    }
                                ]
                            },
                            "rowspan": {
                                "type": "integer",
                                "minimum": 1
                            },
                            "colspan": {
                                "type": "integer",
                                "minimum": 1
                            }
                        },
                        "required": [
                            "content"
                        ]
                    }
                }
            }
        },
        "required": [
            "type",
            "rows",
            "style"
        ]
    },
    "signature_schema": {
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "signature"
                ]
            },
            "content": {
                "type": "string"
            }
        },
        "required": [
            "type",
            "content"
        ]
    },
    "date_schema": {
        "type": "object",
        "properties": {
            "type": {
                "enum": [
                    "date"
                ]
            },
            "content": {
                "type": "string",
                "format": "date"
            }
        },
        "required": [
            "type",
            "content"
        ]
    }
}