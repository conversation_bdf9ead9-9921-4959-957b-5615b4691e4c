from prompts.topic_gen import topic_gen_prompt
from utils.parse_json import get_json_object
import json
from prompts.document_gen_v2 import json_schema_for_doc_gen, document_gen_prompt, example
from openai import OpenAI
import concurrent.futures
from tqdm import tqdm  # 可选进度条

model = "Qwen3-14B"
api_key = "c8f8a40c-59aa-4f75-b5c1-b8541b6c0338"
base_url = "http://kmd-api.kas.wps.cn/api/11334-v3/kQUONi/v1"

client = OpenAI(api_key=api_key, base_url=base_url)


def get_completion(client, prompt, model):
    completion = client.chat.completions.create(
        model=model,
        messages=[
            {"role": "user", "content": prompt}
        ],
    )
    response = completion.choices[0].message.content
    
    return response

def get_completion_streaming(client, prompt, model="Qwen/Qwen2.5-72B-Instruct"):
    completion = client.chat.completions.create(
        model=model,
        messages=[
            {"role": "user", "content": prompt},
        ],
        stream=True,
    )
    response = ""
    for chunk in completion:
        answer_chunk = chunk.choices[0].delta.content

        if answer_chunk != '':
            response += answer_chunk
    return response

def generate_document(industry, topic, client, document_gen_prompt, json_schema_for_doc_gen, example):
    prompt = document_gen_prompt.format(
        industry=industry,
        topic=topic,
        example=example,
        schema=json.dumps(json_schema_for_doc_gen, ensure_ascii=False)
    )
    response = get_completion(client, prompt, model=model)
    
    try:
        doc_obj = get_json_object(response)
        doc_json = json.loads(doc_obj)
        doc_json['industry'] = industry
        return doc_json
    except Exception as e:
        print("Error parsing JSON:", response, e)
        return None

from datetime import date
import os
import argparse
import sys

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Generator引擎数据生成')
    parser.add_argument('--num-samples', type=int, default=2, help='生成样本数量 (默认: 2)')
    args = parser.parse_args()
    
    data_dir = os.getenv("GENERATOR_DATA_DIR", "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/data")

    # 使用命令行参数控制生成数量
    all_document_count = 0
    max_samples = args.num_samples  # 使用传入的参数
    split_samples = min(200, max_samples)  # 确保split_samples不超过总数
    current_date = date.today()
    
    print(f"🎯 目标生成样本数量: {max_samples}")
    print(f"📁 数据输出目录: {data_dir}")

    document_list = []
    
    
    # 只在需要时进行循环，避免无限循环
    while all_document_count < max_samples:
        current_sample_stage = (all_document_count // split_samples + 1) * split_samples
         
        data_gen_dir = os.path.join(data_dir, f"{current_date}_{current_sample_stage}")
        os.makedirs(data_gen_dir, exist_ok=True)
        
        print(f"📊 当前进度: {all_document_count}/{max_samples}")
    
        # 第一步：生成行业和对应的topic和理由
        industry_list = get_completion(client, topic_gen_prompt, model=model)
        industry_topic = get_json_object(industry_list)
        industry_topic_list = json.loads(industry_topic)

        tasks = []
        samples_needed = max_samples - all_document_count
        tasks_created = 0
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(10, samples_needed)) as executor:
            for industry_topic_object in industry_topic_list:
                industry = industry_topic_object["industry"]
                topics = industry_topic_object["topics"]
                for topic in topics:
                    if tasks_created >= samples_needed:
                        break
                    future = executor.submit(
                        generate_document,
                        industry, topic, client,
                        document_gen_prompt, json_schema_for_doc_gen, example
                    )
                    tasks.append(future)
                    tasks_created += 1
                if tasks_created >= samples_needed:
                    break

            # 收集结果
            print(f"🔍 启动 {len(tasks)} 个LLM请求来生成文档...")
            for future in tqdm(concurrent.futures.as_completed(tasks), total=len(tasks)):
                result = future.result()
                if result:
                    document_list.append(result)
                    all_document_count += 1
                    
                    # 达到目标数量时停止
                    if all_document_count >= max_samples:
                        print(f"✅ 已达到目标数量: {all_document_count}")
                        break
                    
                    # 达到split_samples时保存一次
                    if len(document_list) >= split_samples:
                        file_path = os.path.join(data_gen_dir, f"llm_gen_data_{all_document_count}.json")
                        with open(file_path, "w", encoding="utf-8") as f:
                            json.dump(document_list, f, ensure_ascii=False, indent=4)
                        print(f"💾 已保存 {len(document_list)} 个文档到 {file_path}")
                        document_list = []

    # 保存剩余的文档
    if document_list:
        # 保存主要的数据文件（用于图片生成）
        main_file_path = os.path.join(data_gen_dir, "llm_gen_data.json")
        with open(main_file_path, "w", encoding="utf-8") as f:
            json.dump(document_list, f, ensure_ascii=False, indent=4)
        print(f"💾 最终保存 {len(document_list)} 个文档到 {main_file_path}")
        
        # 生成图片
        print("🖼️ 开始生成图片...")
        from image_gen import gen_complex_image_data
        try:
            gen_complex_image_data(data_gen_dir)
            print("✅ 图片生成完成")
        except Exception as e:
            print(f"❌ 图片生成失败: {e}")

    print("Total documents generated:", all_document_count)
