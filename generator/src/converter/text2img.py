import markdown
from pymdownx import tasklist
import os
import hashlib

from playwright.sync_api import sync_playwright
from typing import Optional, List, Dict
import json

def text2html(text):
    html_content = markdown.markdown(text, output_format='html5',
                                     extensions=['tables',
                                                 "fenced_code",
                                                 "extra",
                                                 "pymdownx.tasklist"])

    # Step 2: 内嵌 CSS 样式
    css = """
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        font-size: 14px;
        padding: 1em;
        background-color: #ffffff;
        color: #333333;
    }
    h1, h2, h3, h4, h5, h6 {
        color: #24292e;
    }
    pre {
        background-color: #f6f8fa !important;
        padding: 1em;
        overflow-x: auto;
    }
    code {
        background-color: #ffeef0;
        padding: 0.2em 0.4em;
        border-radius: 3px;
        font-family: <PERSON><PERSON><PERSON>,"Liberation Mono",<PERSON><PERSON>,Courier,monospace;
    }
    a {
        color: #0366d6;
        text-decoration: none;
    }
    a:hover {
        text-decoration: underline;
    }
    /* 表格样式 */
    table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 1em;
    }
    th, td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }
    th {
        background-color: #f2f2f2;
    }
    /* 复选框样式 */
    .task-list-item input[type="checkbox"] {
        margin-right: 0.5em;
    }
    """

    full_html = f"""
    <html>
    <head>
        <meta charset="UTF-8">
        <style>{css}</style>
    </head>
    <body>
        {html_content}
    </body>
    </html>
    """
    return full_html

def get_elements_bbox(page) -> List[Dict]:
    """
    获取页面中所有重要元素的bbox信息
    """
    # JavaScript代码来获取元素的bbox信息
    js_code = """
    () => {
        const elements = [];
        
        // 定义要获取bbox的元素选择器
        const selectors = [
            'h1, h2, h3, h4, h5, h6',  // 标题
            'p',                        // 段落
            'table',                    // 表格
            'td, th',                   // 表格单元格
            'ul, ol',                   // 列表
            'li',                       // 列表项
            'input[type="checkbox"]',   // 复选框
            'div',                      // div容器
            'span'                      // span文本
        ];
        
        selectors.forEach(selector => {
            const elementList = document.querySelectorAll(selector);
            elementList.forEach((el, index) => {
                const rect = el.getBoundingClientRect();
                if (rect.width > 0 && rect.height > 0) {  // 只获取可见元素
                    const tagName = el.tagName.toLowerCase();
                    const textContent = el.textContent?.trim().substring(0, 100) || '';  // 限制文本长度
                    
                    // 判断元素类型
                    let elementType = tagName;
                    if (tagName === 'input' && el.type === 'checkbox') {
                        elementType = 'checkbox';
                    } else if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
                        elementType = 'title';
                    } else if (tagName === 'p') {
                        elementType = 'paragraph';
                    } else if (tagName === 'table') {
                        elementType = 'table';
                    } else if (['td', 'th'].includes(tagName)) {
                        elementType = 'table_cell';
                    } else if (['ul', 'ol'].includes(tagName)) {
                        elementType = 'list';
                    } else if (tagName === 'li') {
                        elementType = 'list_item';
                    }
                    
                    elements.push({
                        type: elementType,
                        bbox: {
                            x: Math.round(rect.left),
                            y: Math.round(rect.top),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        },
                        text: textContent,
                        tag: tagName,
                        selector: `${tagName}:nth-of-type(${index + 1})`
                    });
                }
            });
        });
        
        return elements;
    }
    """
    
    # 执行JavaScript并获取结果
    try:
        elements_data = page.evaluate(js_code)
        return elements_data
    except Exception as e:
        print(f"获取bbox信息时出错: {e}")
        return []

def html2image(full_html, image_dir):
    os.makedirs(image_dir, exist_ok=True)
    hash_obj = hashlib.md5(full_html.encode())
    image_name = f"{hash_obj.hexdigest()}.png"
    image_path = os.path.join(image_dir, image_name)

    # Step 4: 使用 Playwright 渲染 HTML 为图片并获取元素bbox信息
    bbox_data = []
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        page = browser.new_page()
        
        # 设置页面视口大小，增加分辨率
        page.set_viewport_size({"width": 800, "height": 1200})
        
        # 加载 HTML 内容
        page.set_content(full_html)
        
        # 等待页面加载完成（可选）
        page.wait_for_load_state("networkidle")
        
        # 获取所有重要元素的bbox信息
        bbox_data = get_elements_bbox(page)
        
        # 截图并保存
        page.screenshot(path=image_path, full_page=True)
        
        browser.close()

    #     scaned_image_path = os.path.join(image_dir, f"scanned_{image_path}")
    #     water_image_path = os.path.join(image_dir, f"water_{image_path}")


    # scan_params = {
    #     'grayscale': True,
    #     'contrast': 0.2,
    #     'brightness': 80,
    #     'binarize': True,
    #     'thresh_type': 'adaptive',
    #     'add_noise': False,
    #     'noise_type': 'gaussian',
    #     'noise_amount': 0.01,
    #     "add_sharpening": True, 
    #     # 'add_texture': True,
    #     # 'texture_path': 'path/to/your/paper_texture.jpg', # 确保提供一个纸张纹理图片
    #     # 'texture_blend_alpha': 0.15,
    #     'random_rotate': True,
    #     'max_rotate_angle': 5, # 最大旋转角度（度）
    #     'wrapp': True,
    #     "amplitude": 3,
    #     "frequency": 0.01,
    #     'watermark' : True
    # }
    # process_image_to_scan_style(image_path, scaned_image_path, water_image_path,scan_params)

    return image_path, bbox_data

def convert_text2image(text: str, image_dir: str):
    # Step 1: Markdown 转 HTML
    full_html = text2html(text)

    # Step 3: 生成唯一文件名并获取bbox信息
    image_path, bbox_data = html2image(full_html, image_dir)
    return image_path, bbox_data

if __name__ == "__main__":
    text = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>A4 页面布局</title>
    <style>
        body {
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            padding: 10mm;
            box-sizing: border-box;
            font-family: sans-serif;
            background-color: #f9f9f9;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }

        /* 打印样式 */
        @media print {
            body {
                margin: 0;
                padding: 0;
                background-color: white;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>

<body>
    <h2>A0001片的理化检验记录</h2>
    <p>测试结果总结</p>
    <table>
        <tr>
            <th>样品名称</th>
            <td colspan="3">A0001片/瓶装产品（30片/瓶）</td>
        </tr>
        <tr>
            <th>样品类型</th>
            <td>□放行检验 □稳定性考察<br>□其他</td>
            <th>物料代码</th>
            <td></td>
        </tr>
        <tr>
            <th>批号</th>
            <td></td>
            <th>检验编号</th>
            <td></td>
        </tr>
        <tr>
            <th>检验项目</th>
            <td>结果</td>
            <th>可接受标准</th>
            <th>页码</th>
        </tr>
        <tr>
            <td>外观</td>
            <td class="empty">灰色片</td>
            <td>白色至灰白色片</td>
            <td></td>
        </tr>
        <tr>
            <td>鉴别：UV</td>
            <td class="empty"></td>
            <td>供试品溶液的紫外吸收在268nm波长处有最大吸收。</td>
            <td></td>
        </tr>
        <tr>
            <td>鉴别：HPLC</td>
            <td class="empty"></td>
            <td>含量测定项下，供试品溶液主峰的保留时间应与对照品溶液主峰的保留时间一致。</td>
            <td></td>
        </tr>
        <tr>
            <td rowspan="4">含量均匀度</td>
            <td class="empty"></td>
            <td>应符合中国药典通则<0941></td>
            <td rowspan="4"></td>
        </tr>
        <tr>
            <td class="empty"></td>
            <td>应符合美国药典通则<0905></td>
        </tr>
        <tr>
            <td class="empty"></td>
            <td>非指定杂质≤0.20</td>
        </tr>
        <tr>
            <td class="empty"><0.08</td>
            <td>总杂质≤2.0</td>
        </tr>
        <tr>
            <td>溶出度（%）</td>
            <td class="empty"></td>
            <td>≥75（30分钟）<br>Q = 70（30min）</td>
            <td></td>
        </tr>
        <tr>
            <td>水分（%）</td>
            <td class="empty">1.3</td>
            <td>≤3.5</td>
            <td></td>
        </tr>
        <tr>
            <td>含量（%）</td>
            <td class="empty">98.9</td>
            <td>应为标示量的90.0~110.0</td>
            <td></td>
        </tr>
        <tr>
            <td colspan="4">附加检测结果</td>
        </tr>
        <tr>
            <td>对映异构体（%）</td>
            <td class="empty">未检出</td>
            <td>Z4≤0.50</td>
            <td></td>
        </tr>
        <tr>
            <td>备注</td>
            <td colspan="3" class="empty">NA</td>
        </tr>
        <tr>
            <td rowspan="3">结论</td>
            <td colspan="3">□合格 □不合格</td>
        </tr>
        <tr>
            <td colspan="3">与稳定性方案（方案编号：__________）核对，<br>检验项目有：</td>
        </tr>
        <tr>
            <td colspan="3">已完成方案要求项目的检验。（当稳定性样品检验完成填写结论时需填写此项）<br>与稳定性历史数据对比，趋势 □正常 □不正常</td>
        </tr>
        <tr>
            <td>填写人/日期：</td>
            <td class="empty">_________</td>
            <td>复核人/日期：</td>
            <td class="empty">_________</td>
        </tr>
    </table>
</body>
</html>


    """
    html2image(text, 'images')