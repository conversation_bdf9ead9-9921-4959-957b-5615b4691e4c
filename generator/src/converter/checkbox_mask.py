import json
import markdown
import random
# 最后的数据格式
"""
[
    {
        "file_name": "",
        "fields": [
            {
                "field_name": "",
                "field_value": "",
                "field_type": ""
            }
        ],
        "markdown": "",
        "html": "",
        "wrong_blocks": [
            {"line": int, "order": int, "text": str}
        ],
    }
]
"""
checkbox_mapping = {
    "☑": "☐",
    "- [x]": "- [ ]",
    "[✓]": "[ ]",
    "[✔]": "[ ]",
    "☒": "☐",
    "(√)": "( )",
    "■": "□",
}




def get_checkbox_type(html):
    for k, v in checkbox_mapping.items():
        if k in html and v in html:
            return k, v
    
    for k, v in checkbox_mapping.items():
        if v in html or k in html:
            return k, v
import random

def format_checkbox(mark, text,space=1):
    # 生成 0 ~2个空格
    if space == 0:
        space = ""
    else:
        space = " " * space
    return mark + space + text

MARK, ORDER, LEFT, MISS, ALL = "mark", "order", "left", "miss", "all"


# 错误类型1：勾选替换为中文字符错误。
def replace_wrong_mark(checkbox_content, selected=True):
    wrong_content = checkbox_content
    selected_wrong_text_list = ["冈","凶","区","图"]
    unselected_wrong_text_list = ["口", "O", "D"]
    if selected:
        for c in checkbox_mapping.keys():
            if c in checkbox_content:
                chose_text = random.choices(selected_wrong_text_list, weights=[0.25,0.25,0.25,0.25],k=1)[0]
                wrong_content = wrong_content.replace(c, chose_text)
                return wrong_content, checkbox_content
    else:
        for c in checkbox_mapping.values():
            if c in checkbox_content:
                chose_text = random.choices(unselected_wrong_text_list,weights=[0.8, 0.1, 0.1],k=1)[0]
                wrong_content = wrong_content.replace(c, chose_text)
                return wrong_content, checkbox_content


def mask_checkbox(wrong_type, template, checkbox_type, options, wrong_block_probability, wrong_block_list, html_parts, checkbox_html, space=1):
    # print("---------------------------------------------------")
    correct_content = ""
    wrong_content = ""
    for option in options:
        option_text = option.get('option', '')
        checked = 'selected' if option.get('checked') else 'unselected'
        correct_content += template.format(text=format_checkbox(mark=checkbox_type[checked], text=option_text, space=space))

    for option in options:
        option_text = option.get('option', '')
        checked = "selected" if option.get('checked') else "unselected" 

        if checked == "selected":            
            if random.random() < wrong_block_probability[len(wrong_block_list)] and (wrong_type in [LEFT, ALL, MISS, MARK]): # 选中的才有概率会被 masked 掉                        
                if wrong_type == LEFT: # 只有 漏掉勾选的这种类型
                    wrong = format_checkbox(mark=checkbox_type['unselected'], text=option_text, space=space)
                    refined = format_checkbox(mark=checkbox_type['selected'], text=option_text, space=space)
                    checkbox_html += template.format(text=wrong)
                    wrong_block_list.append(
                        {
                            "line": len(html_parts)+1,
                            "wrong": wrong,
                            "refined": refined
                        }
                    )
                elif wrong_type == MISS and random.random() < 0.4:
                    wrong = option_text
                    refined = format_checkbox(mark=checkbox_type['selected'], text=option_text, space=space)
                    checkbox_html += template.format(text=wrong)
                    wrong_block_list.append(
                        {
                            "line": len(html_parts) +1,
                            "wrong": wrong,
                            "refined": refined
                        }
                    )
                elif wrong_type == MARK:
                    refined = format_checkbox(mark=checkbox_type['selected'], text=option_text, space=space)
                    wrong, refined = replace_wrong_mark(refined)
                    checkbox_html += template.format(text=wrong)
                    wrong_block_list.append(
                        {
                            "line": len(html_parts) +1,
                            "wrong": wrong,
                            "refined": refined
                        }
                    )
                elif wrong_type == ALL:
                    # 这里虽然是ALL,但是漏勾选和缺失符号的错误,只选一种。
                    # _wrong_type = random.choice([LEFT, MISS, MARK])
                    _wrong_type = random.choice([LEFT, MARK])
                    if _wrong_type == MISS and random.random() < 0.4:
                        wrong = option_text
                        refined = format_checkbox(mark=checkbox_type[checked], text=option_text, space=space)
                    elif _wrong_type == LEFT:
                        wrong = format_checkbox(mark=checkbox_type['unselected'], text=option_text, space=space)
                        refined = format_checkbox(mark=checkbox_type[checked], text=option_text, space=space)
                    elif _wrong_type == MARK:
                        refined = format_checkbox(mark=checkbox_type['selected'], text=option_text, space=space)                                            
                        wrong, refined = replace_wrong_mark(refined)
                        # print("match mark, ", wrong, refined)
                    else:
                        wrong = format_checkbox(mark=checkbox_type[checked], text=option_text,space=space)
                        refined = wrong

                    
                    checkbox_html += template.format(text=wrong)
                    wrong_content += template.format(text=wrong)

                    if wrong != refined:
                        wrong_block_list.append(
                            {
                                "line": len(html_parts) + 1,
                                "wrong": wrong,
                                "refined": refined
                            }
                        )

            else:                  
                checkbox_html += template.format(text=format_checkbox(mark=checkbox_type[checked], text=option_text, space=space))
                wrong_content += template.format(text=format_checkbox(mark=checkbox_type[checked], text=option_text, space=space))
        else:
            # 这里需要判断以下 MARK 和 MISS 的情况
            if random.random() < wrong_block_probability[len(wrong_block_list)] and wrong_type in [MISS, MARK, ALL]:
                _wrong_type = wrong_type
                if wrong_type == ALL:
                    # _wrong_type = random.choice([MISS, MARK])
                    # 不考虑miss
                    _wrong_type = random.choice([MARK])

                if _wrong_type == MARK:
                    # wrong = format_checkbox(mark="口", text=option_text, space=space)
                    refined = format_checkbox(mark=checkbox_type[checked], text=option_text, space=space)
                    wrong, refined = replace_wrong_mark(refined, selected=False)
                    checkbox_html += template.format(text=wrong)
                
                    wrong_block_list.append(
                            {
                                "line": len(html_parts) +1,
                                "wrong": wrong,
                                "refined": refined
                            }
                        )
                elif _wrong_type == MISS:
                    wrong = option_text
                    refined = format_checkbox(mark=checkbox_type[checked], text=option_text, space=space)
                    checkbox_html += template.format(text=wrong)
                    
                    wrong_block_list.append(
                            {
                                "line": len(html_parts) +1,
                                "wrong": wrong,
                                "refined": refined
                            }
                    )
                wrong_content += wrong
                
            else:
                # 非勾选的情况替换成口
                checkbox_html += template.format(text=format_checkbox(mark=checkbox_type[checked], text=option_text, space=space))
                wrong_content += template.format(text=format_checkbox(mark=checkbox_type[checked], text=option_text, space=space))

    # print("wrong_content: ", wrong_content)

    # print(wrong_content, correct_content)
    # if (wrong_type == ALL or wrong_type == ORDER) and wrong_content != correct_content:
    #     wrong_block_list.append(
    #         {
    #             "line": len(html_parts),
    #             "index": 0,
    #             "wrong": wrong_content,
    #             "refined": correct_content
    #         }
    #     )

    # print(wrong_block_list)
    # print("===============", checkbox_html)
    # print(html_parts)

    return wrong_block_list, checkbox_html

def append_to_html_parts(html_parts, checkbox_html):
    html_parts.extend(checkbox_html)
    return html_parts

"""
# 这里设计五种错误类型
# 1. mark 勾选替换为中文字符错误。
# 2. order 顺序问题, shuffle处理
# 3. left 漏勾选问题
# 4. miss 复选框符号丢失
# 5. all 上述错误的组合
"""
def build_complex_diff_err_type_new_data(json_data, checkbox_type, checkbox_length="short", wrong_type=None):
    checkbox_type = {
        "selected": checkbox_type[0],
        "unselected": checkbox_type[1],
    }
    if checkbox_length == "short":
        wrong_block_probability = [0.65, 0.2, 0.1, 0]
    else:
        wrong_block_probability = [0.8, 0.65, 0.65, 0.6, 0.6, 0.4,0.3,0.3, 0.2, 0.1, 0.05, 0] 

    # 增加部分正样本
    if random.random() < 0.05:
        wrong_block_probability = [0]

    # print(wrong_block_probability)

    need_wrong_block = random.choices([True, False], weights=[1, 0])[0]
    h_template = "<span> {text}</span>"
    empty_template = "{text}"
    v_template = "<div> {text}</div>"
    template_mapping = {
        # "horizontal": h_template,
        # "vertical": v_template,
        "empty": empty_template
    }
    direction = random.choice(list(template_mapping.keys()))
    template = template_mapping[direction]


    html_parts = []
    wrong_blocks = []
    refined_mapping = {}
    wrong_block_list = []

    elements = json_data.get("elements", [])

    for element in elements:

        elements = [0,1,2]
        weights = [0.5,0.4,0.1]
        space = random.choices(elements, weights=weights,k=1)[0]

        type_ = element.get('type')

        if type_ == 'title':

            level = element.get('level', 1)
            content = element.get('content', '')
            html_parts.append(f'<h{level}>{content}</h{level}>')

        elif type_ == 'paragraph':

            content = element.get('content', '')

            html_parts.append(f'<p>{content}</p>')


        elif type_ == 'checkbox':
            key = element.get('key', '')
            options = element.get('options', [])
            checkbox_html = f'<div><strong>{key}:</strong>'

            # print(wrong_type, template, checkbox_type, options, wrong_block_probability, wrong_block_list, html_parts, checkbox_html)
            # print("html parts", html_parts)

            wrong_block_list, checkbox_html = mask_checkbox(wrong_type, template, checkbox_type, options, wrong_block_probability, wrong_block_list, html_parts, checkbox_html, space)
            # print(f"wrong block  list", wrong_block_list)                              

            checkbox_html += '</div>'
            html_parts.append(checkbox_html)

        elif type_ == 'list':
            items = element.get('items', [])

            list_html = '<ul>'
            for item in items:

                list_html += f'<li>{item}</li>'

            list_html += '</ul>'
            html_parts.append(list_html)

        elif type_ == 'table':
            rows = element.get('rows', [])
            table_html = '<table>'
            # todo change to line
            html_parts.append('<table>')

            for row in rows:
                table_html += '<tr>'
                html_parts.append('<tr>')

                for cell in row:
                    content = cell.get('content', '')
                    rowspan = cell.get('rowspan', 1)
                    colspan = cell.get('colspan', 1)
                    cell_content = ""


                    if isinstance(content, str):
                        cell_content = content

                    elif isinstance(content, dict) and 'options' in content:
                        # 检查pre,content是否包含key
                        if content['key'] not in table_html:
                            table_html += f'<td rowspan="1" colspan="1">{content["key"]}</td>'
                            append_to_html_parts(html_parts, ['<td rowspan="1" colspan="1">',f'{content["key"]}',"</td>"])
                        
                        options = content.get('options', [])
                    

                        wrong_block_list, cell_content = mask_checkbox(wrong_type, template, checkbox_type, options, wrong_block_probability, wrong_block_list, html_parts, cell_content, space)

                    elif isinstance(content, list):

                        cell_content += '<ul>'

                        for item in content:

                            cell_content += f'<li>{item}</li>'

                        cell_content += '</ul>'


                    table_html += f'<td rowspan="{rowspan}" colspan="{colspan}">{cell_content}</td>'
                    append_to_html_parts(html_parts, ['<td rowspan="1" colspan="1">',f"{cell_content}","</td>"])

                table_html += '</tr>'
                append_to_html_parts(html_parts, ["</tr>"])

            table_html += '</table>'
            append_to_html_parts(html_parts, ["</table>"])
            # html_parts.append(table_html)

        elif type_ == 'signature':

            content = element.get('content', '')

            # html_parts.append(f'<div><em>Signature: {content}</em></div>')
            append_to_html_parts(html_parts, ["<div><em>Signature: ",f"{content}","</em></div>"])

        elif type_ == 'date':
            content = element.get('content', '')
            # html_parts.append(f'<div><em>Date: {content}</em></div>')
            append_to_html_parts(html_parts, ["<div><em>Date: ",f"{content}","</em></div>"])
    print(f"wrong block  list", wrong_block_list)

    # 这里我们将line的序号加上
    html_parts = ["line {}: {}".format(i, html_parts[i]) for i in range(len(html_parts))]

    return wrong_block_list, "\n".join(html_parts), ""



def build_complex_new_data(json_data, checkbox_type, checkbox_length="short"):
    checkbox_type = {
        "selected": checkbox_type[0],
        "unselected": checkbox_type[1],
    }
    if checkbox_length == "short":
        wrong_block_probability = [0.65, 0.45, 0.25, 0.05, 0.05, 0]
    else:
        wrong_block_probability = [0.65, 0.65, 0.65, 0.65, 0.65, 0.3, 0.1, 0] 

    # 增加部分正样本
    if random.random() < 0.01:
        wrong_block_probability = [0]

    need_wrong_block = random.choices([True, False], weights=[1, 0])[0]
    h_template = "<span> {text}</span>"
    empty_template = "{text}"
    v_template = "<div> {text}</div>"
    template_mapping = {
        "horizontal": h_template,
        "vertical": v_template,
        "empty": empty_template
    }
    direction = random.choice(list(template_mapping.keys()))

    html_parts = []
    wrong_blocks = []
    refined_mapping = {}
    wrong_block_list = []

    elements = json_data.get("elements", [])

    for element in elements:

        type_ = element.get('type')

        if type_ == 'title':

            level = element.get('level', 1)
            content = element.get('content', '')
            html_parts.append(f'<h{level}>{content}</h{level}>')

        elif type_ == 'paragraph':

            content = element.get('content', '')

            html_parts.append(f'<p>{content}</p>')


        elif type_ == 'checkbox':
            key = element.get('key', '')
            options = element.get('options', [])
            checkbox_html = f'<div><strong>{key}:</strong>'
            random.shuffle(options)
            # 这里的option要进行shuffle

            for option in options:
                option_text = option.get('option', '')
                checked = "selected" if option.get('checked') else "unselected" 
                template = template_mapping[direction]
                
                if random.random() < wrong_block_probability[len(wrong_blocks)] and checked == "selected": # 选中的才有概率会被 masked 掉
                    wrong = format_checkbox(mark=checkbox_type['unselected'], text=option_text)
                    refined = format_checkbox(mark=checkbox_type['selected'], text=option_text)
                    checkbox_html += template.format(text=wrong)
                    wrong_block_list.append(
                        {
                            "line": len(html_parts) ,
                            "wrong": wrong,
                            "refined": refined
                        }
                    )
                    
                else:
                    checkbox_html += template.format(text=format_checkbox(mark=checkbox_type[checked], text=option_text))

                  
            checkbox_html += '</div>'
            html_parts.append(checkbox_html)

        elif type_ == 'list':

            items = element.get('items', [])

            list_html = '<ul>'
            for item in items:

                list_html += f'<li>{item}</li>'

            list_html += '</ul>'
            html_parts.append(list_html)

        elif type_ == 'table':
            rows = element.get('rows', [])
            table_html = '<table border="1">'

            for row in rows:
                table_html += '<tr>'

                for cell in row:
                    content = cell.get('content', '')
                    rowspan = cell.get('rowspan', 1)
                    colspan = cell.get('colspan', 1)
                    cell_content = ""


                    if isinstance(content, str):
                        cell_content = content

                    elif isinstance(content, dict) and 'options' in content:
                        # 检查pre,content是否包含key
                        if content['key'] not in table_html:
                            table_html += f'<td rowspan="1" colspan="1">{content["key"]}</td>'


                        
                        options = content.get('options', [])
                        random.shuffle(options)
                        for option in options:
                            option_text = option.get('option', '')
                            checked = "selected" if option.get('checked') else "unselected" 
                            template = template_mapping[direction]
                            
                            if random.random() < wrong_block_probability[len(wrong_blocks)] and checked == "selected": # 选中的才有概率会被 masked 掉
                                wrong = format_checkbox(mark=checkbox_type['unselected'], text=option_text)
                                refined = format_checkbox(mark=checkbox_type['selected'], text=option_text)
                                cell_content += template.format(text=wrong)
                                wrong_block_list.append(
                                    {
                                        "line": len(html_parts),
                                        "wrong": wrong,
                                        "refined": refined
                                    }
                                )
                            else:
                                cell_content += template.format(text=format_checkbox(mark=checkbox_type[checked], text=option_text))

                    elif isinstance(content, list):

                        cell_content += '<ul>'

                        for item in content:

                            cell_content += f'<li>{item}</li>'

                        cell_content += '</ul>'


                    table_html += f'<td rowspan="{rowspan}" colspan="{colspan}">{cell_content}</td>'

                table_html += '</tr>'
            table_html += '</table>'

            html_parts.append(table_html)

        elif type_ == 'signature':

            content = element.get('content', '')

            html_parts.append(f'<div><em>Signature: {content}</em></div>')


        elif type_ == 'date':
            content = element.get('content', '')
            html_parts.append(f'<div><em>Date: {content}</em></div>')

    return wrong_block_list, "\n".join(html_parts), ""

def build_new_data(json_data, checkbox_type, checkbox_length="short"):
    if checkbox_length == "short":
        wrong_block_probability = [0.65, 0.45, 0.25, 0.05, 0.05, 0]
    else:
        wrong_block_probability = [0.65, 0.65, 0.65, 0.65, 0.65, 0.3, 0.1, 0] 

    need_wrong_block = random.choices([True, False], weights=[1, 0])[0]

    markdown_components = []    
    correct_markdown = []
    wrong_blocks = []
    refined_mapping = {}

    for document in json_data.get("documents", []):
        markdown_components.append(f"# {document['title']}")
        correct_markdown.append(f"# {document['title']}")

        
        for section in document.get("sections", []):
            if section["type"] == "markdown":
                markdown_components.append(f"## {section['title']}")
                markdown_components.append(f"{section['content']['text']}")

                correct_markdown.append(f"## {section['title']}")
                correct_markdown.append(f"{section['content']['text']}")
            
            elif section["type"] == "checkbox":
                markdown_components.append(f"## {section['title']}")
                correct_markdown.append(f"## {section['title']}")

                checkbox_list = []
                correct_checkbox_list = []
                for item in section.get("items", []):
                    if item.get("checked"):
                        checkbox_content = f"{checkbox_type[0]} {item['label']}"
                        correct_checkbox_list.append(checkbox_content)
                        # 增加随机数来进行修改内容
                        if random.random() < wrong_block_probability[len(wrong_blocks)] and need_wrong_block:
                            new_checkbox_content = f"{checkbox_type[1]} {item['label']}"
                            refined_mapping[new_checkbox_content] = checkbox_content
                            wrong_blocks.append(new_checkbox_content)
                            checkbox_content = new_checkbox_content

                        
                        checkbox_list.append(checkbox_content)

                    else:
                        checkbox_list.append(f"{checkbox_type[1]} {item['label']}")
                        correct_checkbox_list.append(f"{checkbox_type[1]} {item['label']}")
                
                if checkbox_type[0] == "- [x]":
                    markdown_components.append("\n".join(checkbox_list))
                    correct_markdown.append("\n".join(correct_checkbox_list))
                else:
                    markdown_components.append("\t".join(checkbox_list))
                    correct_markdown.append("\t".join(correct_checkbox_list))

                
                
            elif section["type"] == "confirm":
                markdown_components.append(f"## {section['title']}")
                markdown_components.append(f"{section['content']['text']}")

                correct_markdown.append(f"## {section['title']}")
                correct_markdown.append(f"{section['content']['text']}")
                
            elif section["type"] == "table":
                headers = section.get("headers", [])
                rows = section.get("rows", [])
                
                # Create table header
                markdown_components.append(f"## {section['title']}")
                markdown_components.append(f"| {' | '.join(headers)} |")
                markdown_components.append("| " + " | ".join(["---" for _ in headers]) + " |")

                correct_markdown.append(f"## {section['title']}")
                correct_markdown.append(f"| {' | '.join(headers)} |")
                correct_markdown.append("| " + " | ".join(["---" for _ in headers]) + " |")
                
                # Add table rows
                for row in rows:
                    markdown_components.append(f"| {' | '.join(row)} |")
                    correct_markdown.append(f"| {' | '.join(row)} |")

    
    markdown_content = "\n".join(markdown_components)
    correct_markdown_content = "\n".join(correct_markdown)

    # 这里转换一部分为html
    # if random.random() < 0.3:
    #     markdown_content = markdown.markdown(markdown_content)
    #     correct_markdown_content = markdown.markdown(correct_markdown_content)


    markdown_list = markdown_content.split("\n")
    correct_markdown_list = correct_markdown_content.split("\n")

    # print(len(wrong_blocks))

    wrong_block_list = []
    
    for block in wrong_blocks:
        for i, m in enumerate(markdown_list):
            if block in m:
                wrong_block_list.append(
                    {
                        "line": i,
                        "index": 0,
                        "wrong": block,
                        "refined": refined_mapping[block]
                    }
                )

    # wrong_blcok_list 去重
    deduplicated_list = []
    for w in wrong_block_list:
        ws = f"{w['line']}_{w['index']}_{w['wrong']}"
        match = False
        for d in deduplicated_list:
            d_line = f"{d['line']}_{d['index']}_{d['wrong']}"
            if ws == d_line:
                match = True
        
        if not match:
            deduplicated_list.append(w)
    # print(len(wrong_block_list), len(deduplicated_list), len(wrong_blocks))
    # 去掉一些badcase
    if len(wrong_blocks) != len(deduplicated_list):
        return [], correct_markdown_content, correct_markdown_content

    return deduplicated_list, markdown_content, correct_markdown_content

    

def build_wrong_blocks(train_data_list, json_mapping, checkbox_length="short", complex=False, complex_type=None):
    train_data_index_list = [i for i in range(len(train_data_list)//4)]
    random.shuffle(train_data_index_list)
    train_index = train_data_index_list[int(len(train_data_index_list)*0.05):]
    from collections import defaultdict
    count_statistic = defaultdict(int)
    rebuild_train_data = []
    rebuild_test_data = []
    for i, train_data in enumerate(train_data_list):
        file_name = train_data['image_path']
        markdown = train_data['text']

        json_id = train_data['json_id']
        json_object = json_mapping[f'{json_id}']

        checkbox_type = get_checkbox_type(markdown)
        if checkbox_type == None:
            print(checkbox_type, markdown)
            continue
        if complex:
            if complex_type != None:
                print("start to gen !!!!")
                wrong_block_list, masked_markdown, correct_markdown = build_complex_diff_err_type_new_data(json_object, checkbox_type=checkbox_type, checkbox_length=checkbox_length, wrong_type=complex_type)
            else:
                wrong_block_list, masked_markdown, correct_markdown = build_complex_new_data(json_object, checkbox_type=checkbox_type, checkbox_length=checkbox_length)
        else:  
            wrong_block_list, masked_markdown, correct_markdown = build_new_data(json_object, checkbox_type=checkbox_type, checkbox_length=checkbox_length)

        count_statistic[len(wrong_block_list)] += 1

        if int(json_id) in train_index:        
            rebuild_train_data.append(
                {
                    "file_name": file_name,
                    "correct_markdown": correct_markdown,
                    "masked_markdown": masked_markdown,
                    "wrong_blocks": wrong_block_list
                }
            )
        else:
            rebuild_test_data.append(
                {
                    "file_name": file_name,
                    "correct_markdown": correct_markdown,
                    "masked_markdown": masked_markdown,
                    "wrong_blocks": wrong_block_list
                }
            )

    for i in range(20):
        if i in count_statistic:
            print(i, count_statistic[i])

    return rebuild_train_data, rebuild_test_data

if __name__ == '__main__':
    data = {
        "elements": [
            {
                "type": "table",
                "rows": [
                    [
                        {
                            "content": "疾病名称",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "开始日期",
                            "rowspan": 1,
                            "colspan": 2
                        },
                        {
                            "content": "结束日期",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "是否为家族史",
                            "rowspan": 1,
                            "colspan": 2
                        },
                        {
                            "content": "备注",
                            "rowspan": 1,
                            "colspan": 3
                        }
                    ],
                    [
                        {
                            "content": "糖尿病",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "2018.9.15",
                            "rowspan": 1,
                            "colspan": 2
                        },
                        {
                            "content": "2023.12.30",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "是",
                            "rowspan": 1,
                            "colspan": 2
                        },
                        {
                            "content": "长期使用胰岛素治疗",
                            "rowspan": 1,
                            "colspan": 3
                        }
                    ],
                    [
                        {
                            "content": "",
                            "rowspan": 1,
                            "colspan": 9
                        }
                    ],
                    [
                        {
                            "content": {
                                "type": "checkbox",
                                "key": "过敏史",
                                "options": [
                                    {
                                        "option": "有 (过敏原：牛奶、花生)",
                                        "checked": True
                                    }
                                ]
                            },
                            "rowspan": 1,
                            "colspan": 9
                        }
                    ],
                    [
                        {
                            "content": {
                                "type": "checkbox",
                                "key": "饮酒史",
                                "options": [
                                    {
                                        "option": "有 饮酒20年,每日3杯",
                                        "checked": True
                                    },
                                    {
                                        "option": "无",
                                        "checked": False
                                    }
                                ]
                            },
                            "rowspan": 1,
                            "colspan": 9
                        }
                    ],
                    [
                        {
                            "content": {
                                "type": "checkbox",
                                "key": "吸烟史",
                                "options": [
                                    {
                                        "option": "有 吸烟15年,每日10支",
                                        "checked": True
                                    }
                                ]
                            },
                            "rowspan": 1,
                            "colspan": 9
                        }
                    ],
                    [
                        {
                            "content": {
                                "type": "checkbox",
                                "key": "其他",
                                "options": [
                                    {
                                        "option": "有 (职业病)",
                                        "checked": False
                                    },
                                    {
                                        "option": "无",
                                        "checked": True
                                    }
                                ]
                            },
                            "rowspan": 1,
                            "colspan": 9
                        }
                    ],
                    [
                        {
                            "content": "相关实验室检查(如适用,选填)",
                            "rowspan": 1,
                            "colspan": 9
                        }
                    ],
                    [
                        {
                            "content": "检查项",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "检查日期",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "检查结果",
                            "rowspan": 1,
                            "colspan": 3
                        },
                        {
                            "content": "单位",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "正常值上限",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "正常值下限",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "备注",
                            "rowspan": 1,
                            "colspan": 1
                        }
                    ],
                    [
                        {
                            "content": "白细胞计数",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "2023.6.12",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "5.2",
                            "rowspan": 1,
                            "colspan": 3
                        },
                        {
                            "content": "10*9/L",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "10",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "4",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "↑",
                            "rowspan": 1,
                            "colspan": 1
                        }
                    ],
                    [
                        {
                            "content": "血红蛋白",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "2023.5.28",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "88",
                            "rowspan": 1,
                            "colspan": 3
                        },
                        {
                            "content": "g/L",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "120",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "80",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "↓",
                            "rowspan": 1,
                            "colspan": 1
                        }
                    ],
                    [
                        {
                            "content": "肺部CT平扫",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "2023年12月15日",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "肺部有斑片状阴影,右肺中叶轻度扩张",
                            "rowspan": 1,
                            "colspan": 3
                        },
                        {
                            "content": "",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "建议进一步检查",
                            "rowspan": 1,
                            "colspan": 1
                        }
                    ],
                    [
                        {
                            "content": "心电图",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "2023年9月20日",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "窦性心律,偶发室性早搏",
                            "rowspan": 1,
                            "colspan": 3
                        },
                        {
                            "content": "",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "",
                            "rowspan": 1,
                            "colspan": 1
                        },
                        {
                            "content": "建议心内科随访",
                            "rowspan": 1,
                            "colspan": 1
                        }
                    ]
                ]
            }
        ]
    }

    wrong_type = ALL
    wrong_block, html, _ = build_complex_diff_err_type_new_data(data, ["☑", "☐"], checkbox_length="long", wrong_type=wrong_type)
    print(wrong_block, "\n", html)