import json
import random
import jinja2
templates = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>A4 页面布局</title>
        <style>
                body {
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            padding: 10mm;
            box-sizing: border-box;
            font-family: sans-serif;
            background-color: #f9f9f9;
        }

        table {
            width: 100%;
            margin-top: 20px;
        }

        th, td {          
            padding: 8px;
            text-align: center;
        } 

        h1 {
            text-align: center;
        }
        
        /* 有线无线表 */
        .wire_wireless {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border: 1px solid;
            text-align: left;
        }

        .wire_wireless td {
            border: 1px solid;
        }

        .wire_wireless tr:nth-child(n + 7) td {
            border: none;
        }

        .wire_wireless tr:last-child{
            border: 1px solid;
        }

        /* 三线表 */
        .three_line {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;            
        }

        .three_line tr {
            &:first-child{
                border-bottom: 1px solid;
                border-top:   1px solid ;
            }
          
            &:last-child{
                border-bottom: 1px solid;
            }
        }

        /* 仅外表框的表 */
        .border_only {
            border: 1px solid;
        }

        /* 无线表 */
        .wireless {
            border: none;
            text-align: center;
        }

        /* 有线表 */
        .wire {
            border: 0px;
            width:100%;
            border-collapse: collapse;
        }

        .wire td{
            border: 1px solid;
            text-align: center;
        }

        .invoice {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            text-align: left;
        }

        .invoice tr:nth-child(1) td:nth-last-child(-n+{{k}}) {
            border-bottom: 1px solid black;
        }

        .invoice tr span {
            border-bottom: 1px solid black;
        }

        /* 打印样式 */
        @media print {
            body {
                margin: 0;
                padding: 0;
                background-color: white;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>

<body>
{{body}}
</body>
</html>
"""

tb_mapping = {
    "wire": '<table class="wire">',
    "wireless": '<table class="wireless">',
    "wire-wireless": '<table class="wire_wireless">',
    "three-line": '<table class="three_line">',
    "border-only": '<table class="border_only">',
    "invoice": '<table class="invoice">',
}


checkbox_dict = {
    "markdown": {
        "selected": "- [x]",
        "unselected": "- [ ]"
    },
    "normal": {
        "selected": random.choice(["☑", "[✓]", "[✔]", "☒", "(√)","■"]),
        "unselected": "☐"
    }
}

unselect_dict = {
    "☑": "☐",
    "[✓]": "[ ]",
    "[✔]": "[ ]",
    "☒": "☐",
    "(√)": "( )",
    "■": "□",    
}

def get_checkbox_type(k=3):
    directional = random.choices(["horizontal", "vertical"],k=k)
    
    
    selected_list = random.sample(["☑", "[✓]", "[✔]", "☒", "(√)","■"], k=k)
    
    sampled_list = []
    
    for dir, selected in zip(directional, selected_list):
        sampled_list.append(
            {
                "direction": dir,
                "checkbox": {
                    "selected": selected,
                    "unselected": unselect_dict[selected],
                }
            }
        )
    return sampled_list

def get_checkbox_style():
    if random.random() < 0.5:
        return "markdown"
    else:
        return "normal"

def json2html(json_data, direction="horizontal", checkbox={"selected": "- [x]", "unselected": "- [ ]"}, style="markdown", k=1):
    elements = json_data.get('elements', [])
    
    html_parts = []

    for element in elements:

        type_ = element.get('type')

        if type_ == 'title':

            level = element.get('level', 1)
            content = element.get('content', '')
            html_parts.append(f'<h{level}>{content}</h{level}>')

        elif type_ == 'paragraph':

            content = element.get('content', '')

            html_parts.append(f'<p>{content}</p>')


        elif type_ == 'checkbox':
            key = element.get('key', '')
            options = element.get('options', [])
            checkbox_html = f'<div><strong>{key}:</strong>'

            for option in options:
                option_text = option.get('option', '')
                checked = "selected" if option.get('checked') else "unselected" 
                if direction == "horizontal":
                  checkbox_html += f'<span> {checkbox[checked]} {option_text}</span>'
                else:
                  checkbox_html += f'<div> {checkbox[checked]} {option_text}</div>'
                  
            checkbox_html += '</div>'
            html_parts.append(checkbox_html)

        elif type_ == 'list':

            items = element.get('items', [])

            list_html = '<ul>'
            for item in items:

                list_html += f'<li>{item}</li>'

            list_html += '</ul>'
            html_parts.append(list_html)

        elif type_ == 'table':
            rows = element.get('rows', [])
            style = element.get('style', 'wire')
            caption = element.get('caption', None)
            caption = None
            # style = random.choice(["wire", "wireless", "inner-wireless"])
            table_html = tb_mapping[style]
            if caption != None:
              table_html += f'<caption>{caption}</caption>'
          
            for j,row in enumerate(rows):
                table_html += '<tr>'
                used_line = True if random.random() < 0.5 else False

                for i, cell in enumerate(row):
                    content = cell.get('content', '')
                    rowspan = cell.get('rowspan', 1)
                    colspan = cell.get('colspan', 1)
                    border_left = cell.get('border-left', 1)
                    border_right = cell.get('border-right', 1)
                    border_top = cell.get('border-top', 1)
                    border_bottom = cell.get('border-bottom', 1)
                    cell_content = ""


                    if isinstance(content, str):
                        if used_line and i >= k and j!=0:
                            cell_content = f"<span>{content}</span>"
                        else:
                            cell_content = content

                    elif isinstance(content, dict) and 'options' in content:
                        # 检查pre，content是否包含key
                        if content['key'] not in table_html:
                            table_html += f'<td rowspan="1" colspan="1">{content["key"]}</td>'


                        
                        options = content.get('options', [])
                        for option in options:
                            option_text = option.get('option', '')
                            checked = "selected" if option.get('checked') else "unselected" 
                            if direction == "horizontal":
                              cell_content += f'<span> {checkbox[checked]} {option_text}</span>'
                            else:
                              cell_content += f'<div> {checkbox[checked]} {option_text}</div>'

                    elif isinstance(content, list):

                        cell_content += '<ul>'

                        for item in content:

                            cell_content += f'<li>{item}</li>'

                        cell_content += '</ul>'


                    table_html += f'<td rowspan="{rowspan}" colspan="{colspan}">{cell_content}</td>'

                table_html += '</tr>'
            table_html += '</table>'

            html_parts.append(table_html)

        elif type_ == 'signature':

            content = element.get('content', '')

            html_parts.append(f'<div><em>Signature: {content}</em></div>')


        elif type_ == 'date':
            content = element.get('content', '')
            html_parts.append(f'<div><em>Date: {content}</em></div>')

    return "\n".join(html_parts)

def body2html(body):
    html = jinja2.Template(templates).render(
        body=body,
        k=random.randint(1,3)
    )

    html = html.replace("n + 7", "n + 3")
    return html

# Example usage with the provided JSON data

if __name__ == "__main__":
    # Convert JSON to Markdown
    json_input ={
    "elements": [
      {
        "type": "table",
        "style": "invoice",
        "caption": "测试",
        "rows": [
          [
            {
              "content": "类别",
              "rowspan": 1,
              "colspan": 1,
              "border-left": 0,
              "border-right":0
            },
            {
              "content": "调整前",
              "rowspan": 1,
              "colspan": 1,
              "border-left": 0,
              "border-right":0
            },
            {
              "content": "调整金额",
              "rowspan": 1,
              "colspan": 1,
              "border-left": 0,
              "border-right":0
            },
            {
              "content": "调整后",
              "rowspan": 1,
              "colspan": 1,
              "border-left": 0,
              "border-right":0
            }
          ],
          [
            {
              "content": "资产",
              "rowspan": 1,
              "colspan": 4,
              "border": 0
            }
          ],
          [
            {
              "content": "以公允价值计量且其变动计入当期损益的金融投资",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "705,357",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "688",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "706,045",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            }
          ],
          [
            {
              "content": "以摊余成本计量的金融投资",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "2,450,775",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "(19,151)",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "2,431,624",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            }
          ],
          [
            {
              "content": "以公允价值计量且其变动计入其他综合收益的金融投资",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "799,075",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "18,971",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "818,046",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            }
          ],
          [
            {
              "content": "股东权益",
              "rowspan": 1,
              "colspan": 4,
              "border": 0
            }
          ],
          [
            {
              "content": "其他综合收益",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "(4,069)",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "191",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "(3,878)",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            }
          ],
          [
            {
              "content": "未分配利润",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "292,734",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "127",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            },
            {
              "content": "292,861",
              "rowspan": 1,
              "colspan": 1,
              "border": 0
            }
          ],
          [
            {
              "content": "少数股东权益",
              "rowspan": 1,
              "colspan": 1,
              "border-left": 0,
              "border-right":0,
              "border-top": 0
            },
            {
              "content": "8,040",
              "rowspan": 1,
              "colspan": 1,
              "border-left": 0,
              "border-right":0,
              "border-top": 0
            },
            {
              "content": "190",
              "rowspan": 1,
              "colspan": 1,
              "border-left": 0,
              "border-right":0,
              "border-top": 0
            },
            {
              "content": "8,230",
              "rowspan": 1,
              "colspan": 1,
              "border-left": 0,
              "border-right":0,
              "border-top": 0
            }
          ]
        ]
      }
    ]
  }

    markdown_output = json2html(json_input, direction="horizontal", checkbox={"selected": "[x]", "unselected": "[ ]"}, style="normal")

    html = body2html(markdown_output)
    print(html)