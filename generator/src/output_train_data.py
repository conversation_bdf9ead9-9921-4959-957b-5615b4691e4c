"""
  {
    "checkbox_synetics_train_data_06_19_long": {
    "file_name": os.getenv("GENERATOR_SRC_DATA_DIR", "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/src/data") + "/2025_06_19_2000/train_data_llm_data_train_long.json",
    "formatting": "sharegpt",
    "columns": {
      "messages": "messages",
      "images": "images"
    },
    "tags": {
      "role_tag": "role",
      "content_tag": "content",
      "user_tag": "user",
      "assistant_tag": "assistant"
    }
  }
  }
"""

import os
data_dir = os.getenv("GENERATOR_DATA_DIR", "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/data")
data_list = os.listdir(data_dir)
data_info = {}

samples = ["test_long", "test_short", "train_short", "train_long"]


for dirs in data_list:
    for sample in samples:
        file_name = os.path.join(data_dir, dirs, f"train_data_llm_data_{sample}.json")
        data_info_name = f"{dirs}_data_llm_{sample}"
        print(file_name)
        if os.path.exists(file_name):
            data_info[data_info_name] = {
                "file_name": file_name,
                "formatting": "sharegpt",
                "columns": {
                    "messages": "messages",
                    "images": "images"
                    },
                "tags": {
                    "role_tag": "role",
                    "content_tag": "content",
                    "user_tag": "user",
                    "assistant_tag": "assistant"
                }
            }


import json
data_info_path = os.getenv("GENERATOR_OUTPUT_DIR", "/data/projects/kingsoft/personal/workspace/tablerag/enhance/generator/output") + "/dataset_info.json"
with open(data_info_path, "r") as f:
    data_info_dict = json.load(f)
    data_info_dict.update(data_info)

    f.close()


with open(data_info_path, "w") as f:
    json.dump(data_info_dict,f, indent=2)

print(json.dumps(data_info_dict, indent=2))
print(",".join(list(data_info.keys())))