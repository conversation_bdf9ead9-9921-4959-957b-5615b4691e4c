# 新功能验证文档

本文档描述了新实现的两个功能及其验证方法。

## 功能概述

### 1. 人工标注数据Bbox Canvas渲染
为人工标注数据添加了基于bbox信息的canvas渲染功能，类似现有的Mineru Layout、Mineru VLM、KDC的canvas渲染。

### 2. 版式识别算法Bbox评测指标
为Mineru Layout、Mine<PERSON>（VLM）、KDC KDC三种版式识别算法实现了基于bbox的评测指标，包括精确率、召回率、F1得分、平均IoU、类型准确率等。

## 实现的组件和文件

### 新增文件
1. `analyzer/src/components/AnnotationCanvasRenderer.js` - 人工标注数据Canvas渲染器
2. `analyzer/src/components/BboxMetrics.js` - Bbox评测指标组件
3. `analyzer/src/components/BboxMetrics.css` - Bbox评测指标样式
4. `analyzer/src/utils/bboxMetrics.js` - Bbox评测指标计算工具

### 修改文件
1. `analyzer/src/components/CaseDetail.js` - 集成AnnotationCanvasRenderer
2. `analyzer/src/components/ParseReport.js` - 集成BboxMetrics，为版式识别算法显示专门的评测指标

## 功能验证

### 完整流程测试
使用真实数据进行端到端测试：

1. **数据生成和解析**：
```bash
cd parser && DATASET_NAME=test_bbox_verify ./table_parser.sh --num-tables 2
```

2. **服务启动**：
```bash
./start_tablerag.sh
```

3. **自动化测试结果**：
- ✅ **人工标注数据bbox提取**: 成功提取3个bbox块（title、paragraph、table）
- ✅ **版式识别算法bbox提取**:
  - Mineru Layout: 提取2个bbox（poly格式转换）
  - Mineru VLM: 提取2个bbox（bbox格式）
  - KDC: 提取1个bbox（bounding_box格式）
- ✅ **Bbox评测指标计算**: 精确率100%、召回率100%、F1得分100%、平均IoU 79.43%
- ✅ **数据格式兼容性**: 支持{x,y,width,height}、[x1,y1,x2,y2]、poly等多种格式

### 手动验证步骤

#### 1. 启动服务
```bash
# 使用统一启动脚本
./start_tablerag.sh
```
服务将在以下端口启动：
- 前端服务：http://localhost:3000
- 后端服务：http://localhost:8000

#### 2. 验证人工标注Canvas渲染
1. 在浏览器中打开 http://localhost:3000
2. 选择 **test_bbox_verify** 数据集（包含完整的bbox数据）
3. 点击任意一个测试案例
4. 在案例详情的右侧"人工标注数据"部分，应该能看到：
   - "表格渲染结果"：显示传统的表格渲染
   - "Bbox渲染结果"：显示新的canvas渲染，包含：
     - 不同颜色的bbox区域（标题=橙红色，表格=绿色，段落=蓝色等）
     - 类型图例说明
     - 可点击查看大图
     - 显示原始尺寸和标注块数量

#### 3. 验证版式识别算法Bbox评测指标
1. 在同一个测试案例中，查看"解析详情"部分
2. 对于版式识别算法（**Mineru Layout**、**Mineru（VLM）**、**KDC KDC**），应该看到：
   - 解析状态正常显示
   - **📍 Bbox指标**而不是传统的准确率和TEDS指标
   - Bbox指标包含：
     - 预测数量、真实数量、匹配数量
     - 精确率、召回率、F1得分（百分比显示）
     - 平均IoU、类型准确率
     - 详细匹配信息表格（显示前10个匹配结果）
     - 未匹配统计信息
3. 对于其他OCR算法，仍然显示传统的准确率和TEDS指标

## 技术实现细节

### AnnotationCanvasRenderer组件
- 支持从`bbox_elements`和`elements`中提取bbox信息
- 使用不同颜色区分不同元素类型（标题、表格、段落、复选框等）
- 支持点击查看大图的模态窗口
- 包含颜色图例说明

### BboxMetrics组件
- 计算IoU（Intersection over Union）用于bbox匹配
- 支持多种解析器数据格式（Mineru Layout、Mineru VLM、KDC）
- 提供详细的匹配信息和未匹配统计
- 包含指标说明文字

### Bbox评测指标算法
- **精确率**：正确预测的bbox数量 / 总预测bbox数量
- **召回率**：正确预测的bbox数量 / 总真实bbox数量  
- **F1得分**：精确率和召回率的调和平均数
- **平均IoU**：所有匹配bbox对的IoU平均值
- **类型准确率**：类型匹配的bbox对数量 / 总匹配bbox对数量

## 预期效果

### 人工标注Canvas渲染
- 用户可以直观地看到人工标注数据中各个元素的位置和类型
- 不同类型的元素用不同颜色标识，便于区分
- 支持大图查看，方便详细检查

### 版式识别算法评测
- 版式识别算法不再显示不适用的准确率和TEDS指标
- 显示更合适的bbox定位和类型识别指标
- 提供详细的匹配分析，帮助理解算法性能

## 故障排除

### 如果看不到Bbox渲染结果
1. 检查人工标注数据是否包含bbox信息
2. 查看浏览器控制台是否有JavaScript错误
3. 确认数据集中的标注数据格式正确

### 如果版式识别算法仍显示准确率指标
1. 检查解析器名称是否正确匹配（'Mineru Layout', 'Mineru（VLM）', 'KDC KDC'）
2. 确认ParseReport组件的isLayoutParser逻辑正确
3. 查看浏览器控制台的调试信息

## 总结

新功能已成功实现并通过测试验证。用户现在可以：
1. 在人工标注数据中看到bbox的可视化渲染
2. 为版式识别算法获得更合适的bbox评测指标
3. 更好地理解和分析版式识别算法的性能表现

所有功能都已集成到现有的analyzer界面中，无需额外的配置或操作。
