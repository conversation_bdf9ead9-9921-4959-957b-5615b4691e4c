import logger from './logger';

/**
 * Bbox评测指标计算工具
 * 用于版式识别算法的bbox评估
 */

/**
 * 计算两个bbox的IoU (Intersection over Union)
 * @param {Array} bbox1 - [x1, y1, x2, y2]
 * @param {Array} bbox2 - [x1, y1, x2, y2]
 * @returns {number} IoU值 (0-1)
 */
export const calculateIoU = (bbox1, bbox2) => {
  if (!bbox1 || !bbox2 || bbox1.length < 4 || bbox2.length < 4) {
    return 0;
  }

  const [x1_1, y1_1, x2_1, y2_1] = bbox1;
  const [x1_2, y1_2, x2_2, y2_2] = bbox2;

  // 计算交集区域
  const x1_inter = Math.max(x1_1, x1_2);
  const y1_inter = Math.max(y1_1, y1_2);
  const x2_inter = Math.min(x2_1, x2_2);
  const y2_inter = Math.min(y2_1, y2_2);

  // 如果没有交集
  if (x1_inter >= x2_inter || y1_inter >= y2_inter) {
    return 0;
  }

  // 计算交集面积
  const intersectionArea = (x2_inter - x1_inter) * (y2_inter - y1_inter);

  // 计算并集面积
  const area1 = (x2_1 - x1_1) * (y2_1 - y1_1);
  const area2 = (x2_2 - x1_2) * (y2_2 - y1_2);
  const unionArea = area1 + area2 - intersectionArea;

  return unionArea > 0 ? intersectionArea / unionArea : 0;
};

/**
 * 计算bbox的中心点距离
 * @param {Array} bbox1 - [x1, y1, x2, y2]
 * @param {Array} bbox2 - [x1, y1, x2, y2]
 * @returns {number} 中心点距离
 */
export const calculateCenterDistance = (bbox1, bbox2) => {
  if (!bbox1 || !bbox2 || bbox1.length < 4 || bbox2.length < 4) {
    return Infinity;
  }

  const [x1_1, y1_1, x2_1, y2_1] = bbox1;
  const [x1_2, y1_2, x2_2, y2_2] = bbox2;

  const center1_x = (x1_1 + x2_1) / 2;
  const center1_y = (y1_1 + y2_1) / 2;
  const center2_x = (x1_2 + x2_2) / 2;
  const center2_y = (y1_2 + y2_2) / 2;

  return Math.sqrt(Math.pow(center1_x - center2_x, 2) + Math.pow(center1_y - center2_y, 2));
};

/**
 * 从不同格式的数据中提取bbox信息
 * @param {Object} data - 解析器数据
 * @param {string} parserType - 解析器类型 ('mineru_layout', 'mineru_vlm', 'kdc')
 * @returns {Array} bbox数组，每个元素包含 {bbox, type, text}
 */
export const extractBboxFromParserData = (data, parserType) => {
  const bboxes = [];

  try {
    // 获取页面尺寸信息用于坐标归一化
    const pageInfo = data?.result?.page_info;
    const pageWidth = pageInfo?.width || 1000;
    const pageHeight = pageInfo?.height || 1000;

    switch (parserType) {
      case 'mineru_layout':
        if (data?.result?.layout_data && Array.isArray(data.result.layout_data)) {
          data.result.layout_data.forEach((item, index) => {
            if (item.poly && Array.isArray(item.poly) && item.poly.length >= 8) {
              // poly格式: [x1, y1, x2, y2, x3, y3, x4, y4] - 转换为bbox
              const poly = item.poly;
              const x1 = Math.min(poly[0], poly[2], poly[4], poly[6]);
              const y1 = Math.min(poly[1], poly[3], poly[5], poly[7]);
              const x2 = Math.max(poly[0], poly[2], poly[4], poly[6]);
              const y2 = Math.max(poly[1], poly[3], poly[5], poly[7]);

              // 归一化坐标到0-1范围
              const normalizedBbox = [
                x1 / pageWidth,
                y1 / pageHeight,
                x2 / pageWidth,
                y2 / pageHeight
              ];

              bboxes.push({
                bbox: normalizedBbox,
                type: `category_${item.category_id}`,
                text: item.html || '',
                score: item.score || 0,
                index,
                originalBbox: [x1, y1, x2, y2],
                pageSize: { width: pageWidth, height: pageHeight }
              });
            }
          });
        }
        break;

      case 'mineru_vlm':
        if (data?.result?.layout_data && Array.isArray(data.result.layout_data)) {
          data.result.layout_data.forEach((item, index) => {
            if (item.bbox && Array.isArray(item.bbox) && item.bbox.length >= 4) {
              const [x1, y1, x2, y2] = item.bbox;

              // 归一化坐标到0-1范围
              const normalizedBbox = [
                x1 / pageWidth,
                y1 / pageHeight,
                x2 / pageWidth,
                y2 / pageHeight
              ];

              bboxes.push({
                bbox: normalizedBbox,
                type: item.type || 'unknown',
                text: item.text || '',
                score: item.score || 0,
                index,
                originalBbox: item.bbox,
                pageSize: { width: pageWidth, height: pageHeight }
              });
            }
          });
        }
        break;

      case 'kdc':
        if (data?.result?.data && Array.isArray(data.result.data)) {
          // KDC使用自己的页面尺寸信息
          const kdcPageInfo = data.result.data[0]?.doc?.prop?.page_props?.[0]?.size;
          const kdcPageWidth = kdcPageInfo?.width || pageWidth;
          const kdcPageHeight = kdcPageInfo?.height || pageHeight;

          data.result.data.forEach((docItem, docIndex) => {
            if (docItem.doc && docItem.doc.tree) {
              // 递归处理KDC的树形结构
              const processKdcBlocks = (node, path = []) => {
                if (node.blocks && Array.isArray(node.blocks)) {
                  node.blocks.forEach((block, blockIndex) => {
                    if (block.bounding_box) {
                      const bb = block.bounding_box;
                      const originalBbox = [bb.x1, bb.y1, bb.x2, bb.y2];

                      // 归一化坐标到0-1范围
                      const normalizedBbox = [
                        bb.x1 / kdcPageWidth,
                        bb.y1 / kdcPageHeight,
                        bb.x2 / kdcPageWidth,
                        bb.y2 / kdcPageHeight
                      ];

                      // 提取文本内容
                      let text = '';
                      if (block.textbox && block.textbox.blocks) {
                        text = block.textbox.blocks.map(tb => {
                          if (tb.para && tb.para.runs) {
                            return tb.para.runs.map(run => run.text || '').join('');
                          }
                          return '';
                        }).join(' ');
                      }



                      bboxes.push({
                        bbox: normalizedBbox,
                        type: block.type || 'textbox',
                        text: text.trim(),
                        score: 1.0,
                        index: `${docIndex}-${path.join('-')}-${blockIndex}`,
                        originalBbox: originalBbox,
                        pageSize: { width: kdcPageWidth, height: kdcPageHeight }
                      });
                    }
                  });
                }

                // 递归处理子节点
                if (node.children && Array.isArray(node.children)) {
                  node.children.forEach((child, childIndex) => {
                    processKdcBlocks(child, [...path, childIndex]);
                  });
                }
              };

              processKdcBlocks(docItem.doc.tree, [docIndex]);
            }
          });
        }
        break;

      default:
        logger.warn(`[extractBboxFromParserData] 不支持的解析器类型: ${parserType}`);
    }
  } catch (error) {
    logger.error(`[extractBboxFromParserData] 提取bbox失败:`, error);
  }

  return bboxes;
};

/**
 * 从人工标注数据中提取bbox信息
 * @param {Object} annotationData - 人工标注数据
 * @returns {Array} bbox数组
 */
export const extractBboxFromAnnotation = (annotationData, imageSize = { width: 800, height: 1200 }) => {
  const bboxes = [];

  try {
    // 标注数据的原始图像尺寸（通常是generator生成的800x1200）
    const annotationPageWidth = imageSize.width;
    const annotationPageHeight = imageSize.height;

    // 方法1: 从bbox_elements中获取（[x1, y1, x2, y2]格式）
    if (annotationData?.bbox_elements && Array.isArray(annotationData.bbox_elements)) {
      annotationData.bbox_elements.forEach((item, index) => {
        if (item.bbox && Array.isArray(item.bbox) && item.bbox.length >= 4) {
          // 归一化坐标到0-1范围
          const normalizedBbox = [
            item.bbox[0] / annotationPageWidth,
            item.bbox[1] / annotationPageHeight,
            item.bbox[2] / annotationPageWidth,
            item.bbox[3] / annotationPageHeight
          ];

          bboxes.push({
            bbox: normalizedBbox,
            type: item.type || 'unknown',
            text: item.text || item.content || '',
            index,
            originalBbox: item.bbox,
            pageSize: { width: annotationPageWidth, height: annotationPageHeight }
          });
        }
      });
    }

    // 方法2: 从elements中获取（支持{x, y, width, height}和[x1, y1, x2, y2]格式）
    if (bboxes.length === 0 && annotationData?.elements && Array.isArray(annotationData.elements)) {
      annotationData.elements.forEach((element, elementIndex) => {
        // 只处理有bbox的元素
        if (element.bbox) {
          const bbox = element.bbox;
          let originalBbox;

          if (Array.isArray(bbox) && bbox.length >= 4) {
            // [x1, y1, x2, y2] 格式
            originalBbox = bbox;
          } else if (typeof bbox === 'object' && bbox.x !== undefined) {
            // {x, y, width, height} 格式
            originalBbox = [bbox.x, bbox.y, bbox.x + bbox.width, bbox.y + bbox.height];
          }

          if (originalBbox) {
            // 归一化坐标到0-1范围
            const normalizedBbox = [
              originalBbox[0] / annotationPageWidth,
              originalBbox[1] / annotationPageHeight,
              originalBbox[2] / annotationPageWidth,
              originalBbox[3] / annotationPageHeight
            ];

            bboxes.push({
              bbox: normalizedBbox,
              type: element.type || 'unknown',
              text: element.content || element.caption || element.key || '',
              index: elementIndex,
              originalBbox: originalBbox,
              pageSize: { width: annotationPageWidth, height: annotationPageHeight }
            });
          }
        }
      });
    }

  } catch (error) {
    logger.error(`[extractBboxFromAnnotation] 提取bbox失败:`, error);
  }

  return bboxes;
};

/**
 * 计算bbox评测指标
 * @param {Array} predictedBboxes - 预测的bbox数组
 * @param {Array} groundTruthBboxes - 真实的bbox数组
 * @param {number} iouThreshold - IoU阈值，默认0.5
 * @returns {Object} 评测指标结果
 */
export const calculateBboxMetrics = (predictedBboxes, groundTruthBboxes, iouThreshold = 0.5) => {
  if (!predictedBboxes || !groundTruthBboxes) {
    return {
      precision: 0,
      recall: 0,
      f1Score: 0,
      averageIoU: 0,
      matchedPairs: [],
      unmatchedPredicted: predictedBboxes || [],
      unmatchedGroundTruth: groundTruthBboxes || [],
      totalPredicted: (predictedBboxes || []).length,
      totalGroundTruth: (groundTruthBboxes || []).length,
      totalMatched: 0
    };
  }

  const matchedPairs = [];
  const unmatchedPredicted = [...predictedBboxes];
  const unmatchedGroundTruth = [...groundTruthBboxes];
  let totalIoU = 0;

  // 为每个预测bbox找到最佳匹配的真实bbox
  predictedBboxes.forEach((predBbox, predIndex) => {
    let bestMatch = null;
    let bestIoU = 0;
    let bestGtIndex = -1;

    groundTruthBboxes.forEach((gtBbox, gtIndex) => {
      // 跳过已经匹配的真实bbox
      if (!unmatchedGroundTruth.includes(gtBbox)) return;

      const iou = calculateIoU(predBbox.bbox, gtBbox.bbox);

      if (iou > bestIoU && iou >= iouThreshold) {
        bestMatch = gtBbox;
        bestIoU = iou;
        bestGtIndex = gtIndex;
      }
    });

    // 如果找到了匹配
    if (bestMatch && bestIoU >= iouThreshold) {
      matchedPairs.push({
        predicted: predBbox,
        groundTruth: bestMatch,
        iou: bestIoU,
        centerDistance: calculateCenterDistance(predBbox.bbox, bestMatch.bbox)
      });

      totalIoU += bestIoU;

      // 从未匹配列表中移除
      const predIdx = unmatchedPredicted.findIndex(b => b === predBbox);
      if (predIdx !== -1) unmatchedPredicted.splice(predIdx, 1);

      const gtIdx = unmatchedGroundTruth.findIndex(b => b === bestMatch);
      if (gtIdx !== -1) unmatchedGroundTruth.splice(gtIdx, 1);
    }
  });

  const totalMatched = matchedPairs.length;
  const totalPredicted = predictedBboxes.length;
  const totalGroundTruth = groundTruthBboxes.length;

  // 计算指标
  const precision = totalPredicted > 0 ? totalMatched / totalPredicted : 0;
  const recall = totalGroundTruth > 0 ? totalMatched / totalGroundTruth : 0;
  const f1Score = (precision + recall) > 0 ? (2 * precision * recall) / (precision + recall) : 0;
  const averageIoU = totalMatched > 0 ? totalIoU / totalMatched : 0;

  return {
    precision: Math.round(precision * 100 * 100) / 100, // 保留2位小数的百分比
    recall: Math.round(recall * 100 * 100) / 100,
    f1Score: Math.round(f1Score * 100 * 100) / 100,
    averageIoU: Math.round(averageIoU * 100 * 100) / 100,
    matchedPairs,
    unmatchedPredicted,
    unmatchedGroundTruth,
    totalPredicted,
    totalGroundTruth,
    totalMatched,
    iouThreshold
  };
};

/**
 * 计算类型匹配度
 * @param {Array} matchedPairs - 匹配的bbox对
 * @returns {Object} 类型匹配度结果
 */
export const calculateTypeAccuracy = (matchedPairs) => {
  if (!matchedPairs || matchedPairs.length === 0) {
    return {
      typeAccuracy: 0,
      totalMatched: 0,
      typeMatched: 0,
      typeDetails: []
    };
  }

  let typeMatched = 0;
  const typeDetails = [];

  matchedPairs.forEach((pair, index) => {
    const predType = pair.predicted.type || 'unknown';
    const gtType = pair.groundTruth.type || 'unknown';
    const isTypeMatch = predType === gtType;
    
    if (isTypeMatch) {
      typeMatched++;
    }

    typeDetails.push({
      index,
      predicted: predType,
      groundTruth: gtType,
      isMatch: isTypeMatch,
      iou: pair.iou
    });
  });

  const typeAccuracy = matchedPairs.length > 0 ? (typeMatched / matchedPairs.length) * 100 : 0;

  return {
    typeAccuracy: Math.round(typeAccuracy * 100) / 100,
    totalMatched: matchedPairs.length,
    typeMatched,
    typeDetails
  };
};
