import React, { useRef, useEffect, useState } from 'react';
import logger from '../utils/logger';

/**
 * Mineru Layout Canvas渲染器 - 基于categoryId渲染不同颜色的bbox
 */
const MineruLayoutCanvasRenderer = ({ layoutData, placeholder = "无Mineru Layout渲染结果" }) => {
  const canvasRef = useRef(null);
  const modalCanvasRef = useRef(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [layoutBlocks, setLayoutBlocks] = useState([]);
  const [pageSize, setPageSize] = useState({ width: 1000, height: 1000 });

  // 定义不同categoryId的颜色方案
  const CATEGORY_COLORS = {
    0: { // title
      background: 'rgba(255, 99, 132, ALPHA)',  // 红色 - 标题
      border: '#FF6384',
      name: '标题 (title)'
    },
    1: { // plain_text
      background: 'rgba(54, 162, 235, ALPHA)',  // 蓝色 - 文本
      border: '#36A2EB',
      name: '文本 (plain_text)'
    },
    2: { // abandon
      background: 'rgba(128, 128, 128, ALPHA)', // 灰色 - 废弃内容
      border: '#808080',
      name: '废弃 (abandon)'
    },
    3: { // figure
      background: 'rgba(255, 206, 86, ALPHA)',  // 黄色 - 图像
      border: '#FFCE56',
      name: '图像 (figure)'
    },
    4: { // figure_caption
      background: 'rgba(255, 159, 64, ALPHA)',  // 橙色 - 图像标题
      border: '#FF9F40',
      name: '图像标题 (figure_caption)'
    },
    5: { // table
      background: 'rgba(75, 192, 192, ALPHA)',  // 青色 - 表格
      border: '#4BC0C0',
      name: '表格 (table)'
    },
    6: { // table_caption
      background: 'rgba(153, 102, 255, ALPHA)', // 紫色 - 表格标题
      border: '#9966FF',
      name: '表格标题 (table_caption)'
    },
    7: { // table_footnote
      background: 'rgba(255, 99, 255, ALPHA)',  // 品红色 - 表格脚注
      border: '#FF63FF',
      name: '表格脚注 (table_footnote)'
    },
    8: { // isolate_formula
      background: 'rgba(199, 199, 199, ALPHA)', // 浅灰色 - 独立公式
      border: '#C7C7C7',
      name: '独立公式 (isolate_formula)'
    },
    9: { // formula_caption
      background: 'rgba(83, 102, 255, ALPHA)',  // 靛蓝色 - 公式标题
      border: '#5366FF',
      name: '公式标题 (formula_caption)'
    },
    13: { // embedding
      background: 'rgba(255, 205, 210, ALPHA)', // 浅粉色 - 内嵌公式
      border: '#FFCDD2',
      name: '内嵌公式 (embedding)'
    },
    14: { // isolated
      background: 'rgba(197, 225, 165, ALPHA)', // 浅绿色 - 独立公式
      border: '#C5E1A5',
      name: '独立公式 (isolated)'
    },
    15: { // text
      background: 'rgba(179, 229, 252, ALPHA)', // 浅蓝色 - OCR文本
      border: '#B3E5FC',
      name: 'OCR文本 (text)'
    },
    unknown: {
      background: 'rgba(255, 255, 0, ALPHA)',    // 黄色 - 未知类型
      border: '#FFD700',
      name: '未知类型 (unknown)'
    }
  };

  // 获取类型对应的颜色
  const getCategoryColors = (categoryId, alpha = 0.3) => {
    const colors = CATEGORY_COLORS[categoryId] || CATEGORY_COLORS.unknown;
    return {
      background: colors.background.replace('ALPHA', alpha),
      border: colors.border,
      name: colors.name
    };
  };

  // 处理layout数据，提取bbox信息
  const processLayoutData = (data) => {
    if (!data || !Array.isArray(data)) {
      logger.warn('[MineruLayoutCanvasRenderer] 无效的layout数据');
      return [];
    }

    const blocks = [];
    let maxWidth = 0;
    let maxHeight = 0;

    data.forEach((item, index) => {
      if (!item.poly || !Array.isArray(item.poly) || item.poly.length < 8) {
        logger.warn(`[MineruLayoutCanvasRenderer] 项目 ${index} 缺少有效的poly数据`);
        return;
      }

      // poly格式: [x1, y1, x2, y2, x3, y3, x4, y4] - 四个角的坐标
      // 转换为bbox格式: [x1, y1, x2, y2]
      const poly = item.poly;
      const x1 = Math.min(poly[0], poly[2], poly[4], poly[6]);
      const y1 = Math.min(poly[1], poly[3], poly[5], poly[7]);
      const x2 = Math.max(poly[0], poly[2], poly[4], poly[6]);
      const y2 = Math.max(poly[1], poly[3], poly[5], poly[7]);

      // 更新页面尺寸
      maxWidth = Math.max(maxWidth, x2);
      maxHeight = Math.max(maxHeight, y2);

      blocks.push({
        index,
        categoryId: item.category_id,
        x1, y1, x2, y2,
        width: x2 - x1,
        height: y2 - y1,
        score: item.score || 0,
        html: item.html || null
      });
    });

    // 设置页面尺寸
    setPageSize({ width: maxWidth || 1000, height: maxHeight || 1000 });

    logger.debug(`[MineruLayoutCanvasRenderer] 处理了 ${blocks.length} 个布局块，页面尺寸: ${maxWidth} × ${maxHeight}`);
    return blocks;
  };

  // 初始化数据
  useEffect(() => {
    setIsLoading(true);
    setError(null);

    try {
      const blocks = processLayoutData(layoutData);
      setLayoutBlocks(blocks);
    } catch (err) {
      logger.error('[MineruLayoutCanvasRenderer] 数据处理错误:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [layoutData]);

  // 绘制Canvas
  const drawCanvas = (canvas, scaleFactor) => {
    if (!canvas || !layoutBlocks.length) return;

    const ctx = canvas.getContext('2d');

    // 绘制背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    logger.debug(`[MineruLayoutCanvasRenderer] 绘制 ${layoutBlocks.length} 个布局块，缩放因子: ${scaleFactor}`);

    // 绘制布局块
    layoutBlocks.forEach((block, index) => {
      const x1 = Math.floor(block.x1 * scaleFactor);
      const y1 = Math.floor(block.y1 * scaleFactor);
      const x2 = Math.floor(block.x2 * scaleFactor);
      const y2 = Math.floor(block.y2 * scaleFactor);
      const width = x2 - x1;
      const height = y2 - y1;

      // 跳过无效的块
      if (width <= 0 || height <= 0) {
        logger.warn(`[MineruLayoutCanvasRenderer] 块 ${index} 尺寸无效，跳过`);
        return;
      }

      // 获取类别对应的颜色
      const colors = getCategoryColors(block.categoryId, 0.3);

      // 绘制背景
      ctx.fillStyle = colors.background;
      ctx.fillRect(x1, y1, width, height);

      // 绘制边框
      ctx.strokeStyle = colors.border;
      ctx.lineWidth = 2;
      ctx.strokeRect(x1, y1, width, height);

      // 绘制类别标识
      if (width > 30 && height > 20) {
        ctx.fillStyle = colors.border;
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';
        ctx.textBaseline = 'bottom';
        
        const label = `${block.categoryId}#${index}`;
        const labelX = x1 + 2;
        const labelY = y2 - 2;
        
        // 绘制文本背景
        const textMetrics = ctx.measureText(label);
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.fillRect(labelX - 1, labelY - 12, textMetrics.width + 2, 14);
        
        // 绘制文本
        ctx.fillStyle = colors.border;
        ctx.fillText(label, labelX, labelY);
      }
    });
  };

  // 渲染小图
  useEffect(() => {
    if (layoutBlocks.length > 0 && canvasRef.current) {
      const canvas = canvasRef.current;
      
      // 计算缩放比例
      const maxDisplayWidth = 600;
      const maxDisplayHeight = 400;
      const scaleX = maxDisplayWidth / pageSize.width;
      const scaleY = maxDisplayHeight / pageSize.height;
      const finalScale = Math.min(scaleX, scaleY, 1);

      const displayWidth = Math.floor(pageSize.width * finalScale);
      const displayHeight = Math.floor(pageSize.height * finalScale);

      canvas.width = displayWidth;
      canvas.height = displayHeight;
      canvas.style.width = `${displayWidth}px`;
      canvas.style.height = `${displayHeight}px`;

      drawCanvas(canvas, finalScale);
    }
  }, [layoutBlocks, pageSize]);

  // 打开大图模态窗口
  const openLargeCanvas = () => {
    setShowModal(true);

    setTimeout(() => {
      if (modalCanvasRef.current && layoutBlocks.length > 0) {
        const modalCanvas = modalCanvasRef.current;
        
        // 计算模态窗口尺寸
        const modalWidth = window.innerWidth * 0.85;
        const modalHeight = window.innerHeight * 0.75;
        
        const scaleX = modalWidth / pageSize.width;
        const scaleY = modalHeight / pageSize.height;
        const largeFactor = Math.min(scaleX, scaleY, 2);

        const largeWidth = Math.floor(pageSize.width * largeFactor);
        const largeHeight = Math.floor(pageSize.height * largeFactor);

        modalCanvas.width = largeWidth;
        modalCanvas.height = largeHeight;
        modalCanvas.style.width = `${largeWidth}px`;
        modalCanvas.style.height = `${largeHeight}px`;

        drawCanvas(modalCanvas, largeFactor);
      }
    }, 100);
  };

  // 关闭大图模态窗口
  const closeLargeCanvas = () => {
    setShowModal(false);
  };

  if (isLoading) {
    return (
      <div className="mineru-layout-canvas-container">
        <div className="loading-message">正在渲染Mineru Layout...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mineru-layout-canvas-container">
        <div className="error-message">Mineru Layout渲染错误: {error}</div>
      </div>
    );
  }

  if (!layoutData || !Array.isArray(layoutData) || layoutData.length === 0) {
    return (
      <div className="mineru-layout-canvas-container">
        <div className="placeholder-message">{placeholder}</div>
      </div>
    );
  }

  return (
    <div className="mineru-layout-canvas-container">
      <div style={{ border: '1px solid #ddd', borderRadius: '4px', background: '#f8f9fa', padding: '10px' }}>
        {/* 颜色图例 - 只显示当前案例中存在的类型 */}
        <div style={{ marginBottom: '10px', padding: '8px', background: '#ffffff', borderRadius: '4px', border: '1px solid #e0e0e0' }}>
          <div style={{ fontSize: '12px', fontWeight: 'bold', marginBottom: '5px', color: '#333' }}>类型图例：</div>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', fontSize: '10px' }}>
            {(() => {
              // 获取当前案例中存在的categoryId
              const existingCategories = [...new Set(layoutBlocks.map(block => block.categoryId))];
              return existingCategories.map(categoryId => {
                const colors = CATEGORY_COLORS[categoryId] || CATEGORY_COLORS.unknown;
                return (
                  <div key={categoryId} style={{ display: 'flex', alignItems: 'center', gap: '3px' }}>
                    <div style={{
                      width: '12px',
                      height: '12px',
                      backgroundColor: colors.background.replace('ALPHA', '0.3'),
                      border: `1px solid ${colors.border}`,
                      borderRadius: '2px'
                    }}></div>
                    <span>{colors.name}</span>
                  </div>
                );
              });
            })()}
          </div>
        </div>
        
        <canvas
          ref={canvasRef}
          style={{
            border: '1px solid #ccc',
            background: 'white',
            display: 'block',
            margin: '0 auto',
            cursor: 'pointer',
            maxWidth: '100%',
            height: 'auto'
          }}
          onClick={openLargeCanvas}
        />
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#666', textAlign: 'center' }}>
          原始尺寸: {pageSize.width} × {pageSize.height} | 布局块数量: {layoutBlocks.length}
          <br />
          <span style={{ color: '#007bff', cursor: 'pointer' }} onClick={openLargeCanvas}>
            🔍 点击图片查看大图
          </span>
        </div>
      </div>

      {/* 大图模态窗口 */}
      {showModal && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}
          onClick={closeLargeCanvas}
        >
          <div
            style={{
              backgroundColor: 'white',
              borderRadius: '8px',
              padding: '20px',
              maxWidth: '90%',
              maxHeight: '90%',
              overflow: 'auto',
              display: 'flex',
              flexDirection: 'column'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
              <h3 style={{ margin: 0 }}>Mineru Layout 大图预览</h3>
              <button
                onClick={closeLargeCanvas}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '24px',
                  cursor: 'pointer',
                  color: '#666'
                }}
              >
                ×
              </button>
            </div>
            
            <div
              style={{
                flex: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'auto',
                background: '#f8f9fa',
                borderRadius: '4px'
              }}
            >
              <canvas
                ref={modalCanvasRef}
                style={{
                  border: '1px solid #ccc',
                  background: 'white',
                  maxWidth: '95%',
                  maxHeight: '95%',
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MineruLayoutCanvasRenderer;
