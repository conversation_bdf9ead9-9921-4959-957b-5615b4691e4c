import React, { useRef, useEffect, useState } from 'react';
import logger from '../utils/logger';

/**
 * Mineru VLM Canvas渲染器 - 解析model_output.txt格式并渲染bbox
 */
const MineruVlmCanvasRenderer = ({ modelOutputText, placeholder = "无Mineru VLM渲染结果" }) => {
  const canvasRef = useRef(null);
  const modalCanvasRef = useRef(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [vlmBlocks, setVlmBlocks] = useState([]);
  const [pageSize, setPageSize] = useState({ width: 1000, height: 1000 });

  // 定义不同ref类型的颜色方案
  const REF_COLORS = {
    title: {
      background: 'rgba(255, 99, 132, ALPHA)',  // 红色 - 标题
      border: '#FF6384',
      name: '标题 (title)'
    },
    text: {
      background: 'rgba(54, 162, 235, ALPHA)',  // 蓝色 - 文本
      border: '#36A2EB',
      name: '文本 (text)'
    },
    table: {
      background: 'rgba(75, 192, 192, ALPHA)',  // 青色 - 表格
      border: '#4BC0C0',
      name: '表格 (table)'
    },
    figure: {
      background: 'rgba(255, 206, 86, ALPHA)',  // 黄色 - 图像
      border: '#FFCE56',
      name: '图像 (figure)'
    },
    unknown: {
      background: 'rgba(255, 255, 0, ALPHA)',    // 黄色 - 未知类型
      border: '#FFD700',
      name: '未知类型 (unknown)'
    }
  };

  // 获取类型对应的颜色
  const getRefColors = (refType, alpha = 0.3) => {
    const colors = REF_COLORS[refType] || REF_COLORS.unknown;
    return {
      background: colors.background.replace('ALPHA', alpha),
      border: colors.border,
      name: colors.name
    };
  };

  // 解析model_output.txt格式的文本
  const parseModelOutput = (text) => {
    if (!text || typeof text !== 'string') {
      logger.warn('[MineruVlmCanvasRenderer] 无效的model_output文本');
      return [];
    }

    const blocks = [];
    let maxWidth = 0;
    let maxHeight = 0;

    // 使用正则表达式匹配每个块，支持跨行匹配
    const blockRegex = /<\|box_start\|>(\d+)\s+(\d+)\s+(\d+)\s+(\d+)<\|box_end\|><\|ref_start\|>([^<]+)<\|ref_end\|><\|md_start\|>([\s\S]*?)<\|md_end\|>/g;
    
    let match;
    let index = 0;
    
    while ((match = blockRegex.exec(text)) !== null) {
      const [, x1Str, y1Str, x2Str, y2Str, refType, content] = match;
      
      const x1 = parseInt(x1Str, 10);
      const y1 = parseInt(y1Str, 10);
      const x2 = parseInt(x2Str, 10);
      const y2 = parseInt(y2Str, 10);

      // 验证坐标
      if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {
        logger.warn(`[MineruVlmCanvasRenderer] 块 ${index} 坐标无效: ${x1Str}, ${y1Str}, ${x2Str}, ${y2Str}`);
        continue;
      }

      // 更新页面尺寸
      maxWidth = Math.max(maxWidth, x2);
      maxHeight = Math.max(maxHeight, y2);

      blocks.push({
        index,
        refType: refType.trim(),
        x1, y1, x2, y2,
        width: x2 - x1,
        height: y2 - y1,
        content: content.trim()
      });

      index++;
    }

    // 设置页面尺寸，添加一些边距
    setPageSize({
      width: maxWidth > 0 ? maxWidth + 50 : 1000,
      height: maxHeight > 0 ? maxHeight + 50 : 1000
    });

    logger.debug(`[MineruVlmCanvasRenderer] 解析了 ${blocks.length} 个VLM块，页面尺寸: ${maxWidth} × ${maxHeight}`);
    return blocks;
  };

  // 初始化数据
  useEffect(() => {
    setIsLoading(true);
    setError(null);

    try {
      const blocks = parseModelOutput(modelOutputText);
      setVlmBlocks(blocks);
    } catch (err) {
      logger.error('[MineruVlmCanvasRenderer] 数据处理错误:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [modelOutputText]);

  // 绘制Canvas
  const drawCanvas = (canvas, scaleFactor) => {
    if (!canvas || !vlmBlocks.length) return;

    const ctx = canvas.getContext('2d');

    // 绘制背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    logger.debug(`[MineruVlmCanvasRenderer] 绘制 ${vlmBlocks.length} 个VLM块，缩放因子: ${scaleFactor}`);

    // 绘制VLM块
    vlmBlocks.forEach((block, index) => {
      const x1 = Math.floor(block.x1 * scaleFactor);
      const y1 = Math.floor(block.y1 * scaleFactor);
      const x2 = Math.floor(block.x2 * scaleFactor);
      const y2 = Math.floor(block.y2 * scaleFactor);
      const width = x2 - x1;
      const height = y2 - y1;

      // 跳过无效的块
      if (width <= 0 || height <= 0) {
        logger.warn(`[MineruVlmCanvasRenderer] 块 ${index} 尺寸无效，跳过`);
        return;
      }

      // 获取类型对应的颜色
      const colors = getRefColors(block.refType, 0.3);

      // 绘制背景
      ctx.fillStyle = colors.background;
      ctx.fillRect(x1, y1, width, height);

      // 绘制边框
      ctx.strokeStyle = colors.border;
      ctx.lineWidth = 2;
      ctx.strokeRect(x1, y1, width, height);

      // 绘制类型标识
      if (width > 40 && height > 20) {
        ctx.fillStyle = colors.border;
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';
        ctx.textBaseline = 'bottom';
        
        const label = `${block.refType}#${index}`;
        const labelX = x1 + 2;
        const labelY = y2 - 2;
        
        // 绘制文本背景
        const textMetrics = ctx.measureText(label);
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.fillRect(labelX - 1, labelY - 12, textMetrics.width + 2, 14);
        
        // 绘制文本
        ctx.fillStyle = colors.border;
        ctx.fillText(label, labelX, labelY);
      }
    });
  };

  // 渲染小图
  useEffect(() => {
    if (vlmBlocks.length > 0 && canvasRef.current) {
      const canvas = canvasRef.current;
      
      // 计算缩放比例
      const maxDisplayWidth = 600;
      const maxDisplayHeight = 400;
      const scaleX = maxDisplayWidth / pageSize.width;
      const scaleY = maxDisplayHeight / pageSize.height;
      const finalScale = Math.min(scaleX, scaleY, 1);

      const displayWidth = Math.floor(pageSize.width * finalScale);
      const displayHeight = Math.floor(pageSize.height * finalScale);

      canvas.width = displayWidth;
      canvas.height = displayHeight;
      canvas.style.width = `${displayWidth}px`;
      canvas.style.height = `${displayHeight}px`;

      drawCanvas(canvas, finalScale);
    }
  }, [vlmBlocks, pageSize]);

  // 打开大图模态窗口
  const openLargeCanvas = () => {
    setShowModal(true);

    setTimeout(() => {
      if (modalCanvasRef.current && vlmBlocks.length > 0) {
        const modalCanvas = modalCanvasRef.current;
        
        // 计算模态窗口尺寸
        const modalWidth = window.innerWidth * 0.85;
        const modalHeight = window.innerHeight * 0.75;
        
        const scaleX = modalWidth / pageSize.width;
        const scaleY = modalHeight / pageSize.height;
        const largeFactor = Math.min(scaleX, scaleY, 2);

        const largeWidth = Math.floor(pageSize.width * largeFactor);
        const largeHeight = Math.floor(pageSize.height * largeFactor);

        modalCanvas.width = largeWidth;
        modalCanvas.height = largeHeight;
        modalCanvas.style.width = `${largeWidth}px`;
        modalCanvas.style.height = `${largeHeight}px`;

        drawCanvas(modalCanvas, largeFactor);
      }
    }, 100);
  };

  // 关闭大图模态窗口
  const closeLargeCanvas = () => {
    setShowModal(false);
  };

  if (isLoading) {
    return (
      <div className="mineru-vlm-canvas-container">
        <div className="loading-message">正在渲染Mineru VLM...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mineru-vlm-canvas-container">
        <div className="error-message">Mineru VLM渲染错误: {error}</div>
      </div>
    );
  }

  if (!modelOutputText || typeof modelOutputText !== 'string' || modelOutputText.trim() === '') {
    return (
      <div className="mineru-vlm-canvas-container">
        <div className="placeholder-message">{placeholder}</div>
      </div>
    );
  }

  return (
    <div className="mineru-vlm-canvas-container">
      <div style={{ border: '1px solid #ddd', borderRadius: '4px', background: '#f8f9fa', padding: '10px' }}>
        {/* 颜色图例 */}
        <div style={{ marginBottom: '10px', padding: '8px', background: '#ffffff', borderRadius: '4px', border: '1px solid #e0e0e0' }}>
          <div style={{ fontSize: '12px', fontWeight: 'bold', marginBottom: '5px', color: '#333' }}>类型图例：</div>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', fontSize: '10px' }}>
            {Object.entries(REF_COLORS).filter(([key]) => key !== 'unknown').map(([refType, colors]) => (
              <div key={refType} style={{ display: 'flex', alignItems: 'center', gap: '3px' }}>
                <div style={{ 
                  width: '12px', 
                  height: '12px', 
                  backgroundColor: colors.background.replace('ALPHA', '0.3'),
                  border: `1px solid ${colors.border}`,
                  borderRadius: '2px'
                }}></div>
                <span>{colors.name}</span>
              </div>
            ))}
          </div>
        </div>
        
        <canvas
          ref={canvasRef}
          style={{
            border: '1px solid #ccc',
            background: 'white',
            display: 'block',
            margin: '0 auto',
            cursor: 'pointer',
            maxWidth: '100%',
            height: 'auto'
          }}
          onClick={openLargeCanvas}
        />
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#666', textAlign: 'center' }}>
          原始尺寸: {pageSize.width} × {pageSize.height} | VLM块数量: {vlmBlocks.length}
          <br />
          <span style={{ color: '#007bff', cursor: 'pointer' }} onClick={openLargeCanvas}>
            🔍 点击图片查看大图
          </span>
        </div>
      </div>

      {/* 大图模态窗口 */}
      {showModal && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}
          onClick={closeLargeCanvas}
        >
          <div
            style={{
              backgroundColor: 'white',
              borderRadius: '8px',
              padding: '20px',
              maxWidth: '90%',
              maxHeight: '90%',
              overflow: 'auto',
              display: 'flex',
              flexDirection: 'column'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
              <h3 style={{ margin: 0 }}>Mineru VLM 大图预览</h3>
              <button
                onClick={closeLargeCanvas}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '24px',
                  cursor: 'pointer',
                  color: '#666'
                }}
              >
                ×
              </button>
            </div>
            
            <div
              style={{
                flex: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'auto',
                background: '#f8f9fa',
                borderRadius: '4px'
              }}
            >
              <canvas
                ref={modalCanvasRef}
                style={{
                  border: '1px solid #ccc',
                  background: 'white',
                  maxWidth: '95%',
                  maxHeight: '95%',
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MineruVlmCanvasRenderer;
