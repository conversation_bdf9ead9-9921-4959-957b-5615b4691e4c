import React, { useMemo } from 'react';
import logger from '../utils/logger';
import { 
  extractBboxFromParserData, 
  extractBboxFromAnnotation, 
  calculateBboxMetrics, 
  calculateTypeAccuracy 
} from '../utils/bboxMetrics';
import './BboxMetrics.css';

/**
 * Bbox评测指标组件
 * 显示版式识别算法的bbox评估结果
 */
const BboxMetrics = ({ 
  parserName, 
  parserData, 
  parserType, 
  annotationData, 
  hasAnnotationData 
}) => {
  
  // 计算bbox评测指标
  const bboxMetricsResult = useMemo(() => {
    logger.debug(`[BboxMetrics] ${parserName}: 开始计算指标`, {
      hasAnnotationData,
      hasAnnotationDataValue: !!annotationData,
      hasParserData: !!parserData,
      parserType,
      annotationDataType: typeof annotationData,
      parserDataType: typeof parserData
    });

    if (!hasAnnotationData || !annotationData || !parserData) {
      logger.warn(`[BboxMetrics] ${parserName}: 缺少必要数据`, {
        hasAnnotationData,
        hasAnnotationDataValue: !!annotationData,
        hasParserData: !!parserData
      });
      return null;
    }

    try {
      // 提取预测的bbox
      const predictedBboxes = extractBboxFromParserData(parserData, parserType);

      // 提取真实的bbox
      const groundTruthBboxes = extractBboxFromAnnotation(annotationData);

      logger.debug(`[BboxMetrics] ${parserName}: 提取bbox`, {
        predictedCount: predictedBboxes.length,
        groundTruthCount: groundTruthBboxes.length,
        predictedSample: predictedBboxes.slice(0, 2),
        groundTruthSample: groundTruthBboxes.slice(0, 2)
      });

      if (predictedBboxes.length === 0 && groundTruthBboxes.length === 0) {
        logger.warn(`[BboxMetrics] ${parserName}: 没有bbox数据`);
        return null;
      }

      // 计算bbox指标
      const metrics = calculateBboxMetrics(predictedBboxes, groundTruthBboxes, 0.5);

      // 计算类型匹配度
      const typeAccuracy = calculateTypeAccuracy(metrics.matchedPairs);

      logger.debug(`[BboxMetrics] ${parserName}: 计算完成`, {
        precision: metrics.precision,
        recall: metrics.recall,
        f1Score: metrics.f1Score,
        totalMatched: metrics.totalMatched
      });

      return {
        ...metrics,
        typeAccuracy: typeAccuracy.typeAccuracy,
        typeDetails: typeAccuracy.typeDetails,
        hasData: true
      };
    } catch (error) {
      logger.error(`[BboxMetrics] ${parserName}: 计算bbox指标失败`, error);
      return null;
    }
  }, [parserName, parserData, parserType, annotationData, hasAnnotationData]);

  if (!hasAnnotationData) {
    return (
      <div className="bbox-metrics no-data">
        <div className="metrics-header">
          <h5>📍 Bbox指标</h5>
        </div>
        <div className="metrics-content">
          <p className="no-data-message">无标注数据，无法计算Bbox指标</p>
        </div>
      </div>
    );
  }

  if (!bboxMetricsResult) {
    return (
      <div className="bbox-metrics no-result">
        <div className="metrics-header">
          <h5>📍 Bbox指标</h5>
        </div>
        <div className="metrics-content">
          <p className="no-result-message">
            无法计算Bbox指标 - 可能是bbox数据缺失或格式不兼容
          </p>
        </div>
      </div>
    );
  }

  const {
    precision,
    recall,
    f1Score,
    averageIoU,
    typeAccuracy,
    totalPredicted,
    totalGroundTruth,
    totalMatched,
    matchedPairs,
    unmatchedPredicted,
    unmatchedGroundTruth
  } = bboxMetricsResult;

  return (
    <div className="bbox-metrics">
      <div className="metrics-header">
        <h5>📍 Bbox指标</h5>
      </div>
      
      <div className="metrics-summary">
        <div className="metrics-row">
          <div className="metric-item">
            <span className="metric-label">预测数量:</span>
            <span className="metric-value">{totalPredicted}</span>
          </div>
          <div className="metric-item">
            <span className="metric-label">真实数量:</span>
            <span className="metric-value">{totalGroundTruth}</span>
          </div>
          <div className="metric-item">
            <span className="metric-label">匹配数量:</span>
            <span className="metric-value">{totalMatched}</span>
          </div>
        </div>
        
        <div className="metrics-row">
          <div className="metric-item primary">
            <span className="metric-label">精确率:</span>
            <span className="metric-value highlight">{precision}%</span>
          </div>
          <div className="metric-item primary">
            <span className="metric-label">召回率:</span>
            <span className="metric-value highlight">{recall}%</span>
          </div>
          <div className="metric-item primary">
            <span className="metric-label">F1得分:</span>
            <span className="metric-value highlight">{f1Score}%</span>
          </div>
        </div>
        
        <div className="metrics-row">
          <div className="metric-item">
            <span className="metric-label">平均IoU:</span>
            <span className="metric-value">{averageIoU}%</span>
          </div>
          <div className="metric-item">
            <span className="metric-label">类型准确率:</span>
            <span className="metric-value">{typeAccuracy}%</span>
          </div>
        </div>
      </div>

      {/* 详细匹配信息 */}
      {matchedPairs.length > 0 && (
        <div className="bbox-details">
          <div className="details-header">
            <h6>匹配详情 (前10个)</h6>
          </div>
          <div className="details-table">
            <table>
              <thead>
                <tr>
                  <th>序号</th>
                  <th>预测类型</th>
                  <th>真实类型</th>
                  <th>IoU</th>
                  <th>类型匹配</th>
                </tr>
              </thead>
              <tbody>
                {matchedPairs.slice(0, 10).map((pair, index) => (
                  <tr key={index}>
                    <td>{index + 1}</td>
                    <td>{pair.predicted.type || 'unknown'}</td>
                    <td>{pair.groundTruth.type || 'unknown'}</td>
                    <td>{Math.round(pair.iou * 100)}%</td>
                    <td>
                      <span className={`match-status ${
                        (pair.predicted.type || 'unknown') === (pair.groundTruth.type || 'unknown') 
                          ? 'match' : 'no-match'
                      }`}>
                        {(pair.predicted.type || 'unknown') === (pair.groundTruth.type || 'unknown') ? '✓' : '✗'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {matchedPairs.length > 10 && (
            <div className="details-footer">
              <small>显示前10个匹配结果，共{matchedPairs.length}个匹配</small>
            </div>
          )}
        </div>
      )}

      {/* 未匹配信息 */}
      {(unmatchedPredicted.length > 0 || unmatchedGroundTruth.length > 0) && (
        <div className="unmatched-info">
          <div className="unmatched-row">
            {unmatchedPredicted.length > 0 && (
              <div className="unmatched-item">
                <span className="unmatched-label">未匹配预测:</span>
                <span className="unmatched-value">{unmatchedPredicted.length}个</span>
              </div>
            )}
            {unmatchedGroundTruth.length > 0 && (
              <div className="unmatched-item">
                <span className="unmatched-label">未匹配真实:</span>
                <span className="unmatched-value">{unmatchedGroundTruth.length}个</span>
              </div>
            )}
          </div>

          {/* 详细的未匹配信息表格 */}
          {(unmatchedPredicted.length > 0 || unmatchedGroundTruth.length > 0) && (
            <div className="unmatched-details">
              <div className="details-header">
                <h6>未匹配详情</h6>
              </div>

              {/* 未匹配的预测bbox */}
              {unmatchedPredicted.length > 0 && (
                <div className="unmatched-section">
                  <h7>未匹配的预测bbox (共{unmatchedPredicted.length}个):</h7>
                  <div className="details-table scrollable">
                    <table>
                      <thead>
                        <tr>
                          <th>序号</th>
                          <th>类型</th>
                          <th>位置 [x1,y1,x2,y2]</th>
                          <th>尺寸</th>
                          <th>内容预览</th>
                        </tr>
                      </thead>
                      <tbody>
                        {unmatchedPredicted.map((bbox, index) => (
                          <tr key={`pred-${index}`}>
                            <td>{index + 1}</td>
                            <td>{bbox.type || 'unknown'}</td>
                            <td>[{bbox.bbox.map(v => Math.round(v)).join(', ')}]</td>
                            <td>{Math.round(bbox.bbox[2] - bbox.bbox[0])} × {Math.round(bbox.bbox[3] - bbox.bbox[1])}</td>
                            <td title={bbox.text}>
                              {bbox.text ? (bbox.text.length > 20 ? bbox.text.substring(0, 20) + '...' : bbox.text) : '-'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* 未匹配的真实bbox */}
              {unmatchedGroundTruth.length > 0 && (
                <div className="unmatched-section">
                  <h7>未匹配的真实bbox (共{unmatchedGroundTruth.length}个):</h7>
                  <div className="details-table scrollable">
                    <table>
                      <thead>
                        <tr>
                          <th>序号</th>
                          <th>类型</th>
                          <th>位置 [x1,y1,x2,y2]</th>
                          <th>尺寸</th>
                          <th>内容预览</th>
                        </tr>
                      </thead>
                      <tbody>
                        {unmatchedGroundTruth.map((bbox, index) => (
                          <tr key={`gt-${index}`}>
                            <td>{index + 1}</td>
                            <td>{bbox.type || 'unknown'}</td>
                            <td>[{bbox.bbox.map(v => Math.round(v)).join(', ')}]</td>
                            <td>{Math.round(bbox.bbox[2] - bbox.bbox[0])} × {Math.round(bbox.bbox[3] - bbox.bbox[1])}</td>
                            <td title={bbox.text}>
                              {bbox.text ? (bbox.text.length > 20 ? bbox.text.substring(0, 20) + '...' : bbox.text) : '-'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
      
      <div className="bbox-explanation">
        <small>
          Bbox指标评估版式识别的定位准确性。精确率表示预测bbox的准确性，
          召回率表示真实bbox的检出率，F1得分是两者的调和平均数。
          IoU阈值设为50%，类型准确率评估元素类型识别的正确性。
        </small>
      </div>
    </div>
  );
};

export default BboxMetrics;
