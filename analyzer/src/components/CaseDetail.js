import React, { useState, memo } from 'react';
import logger from '../utils/logger';
import ImageModal from './ImageModal';
import ParserRow from './ParserRow';
import TextboxDistributionChart from './TextboxDistributionChart';
import MetricsPanel from './MetricsPanel';
import MetricsSummaryChart from './MetricsSummaryChart';
import TableRenderer from './TableRenderer';
import AnnotationCanvasRenderer from './AnnotationCanvasRenderer';
import { getAvailableParserConfigs } from '../utils/parserConfig';
import { useParserContext } from '../contexts/ParserContext';
import './CaseDetail.css';

const CaseDetail = memo(({
  caseData,
  dataset,
  annotationData,
  annotationsLoading
}) => {
  const [imageModalOpen, setImageModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showOriginalText, setShowOriginalText] = useState(false); // 默认隐藏原始文本列

  // 使用解析器上下文
  const { enabledParsers } = useParserContext();

  // 标注数据现在通过props传入，不再需要useEffect获取

  if (!caseData) {
    return (
      <div className="case-detail">
        <div className="case-detail-empty">
          请选择一个测试案例查看详情
        </div>
      </div>
    );
  }

  const {
    index,
    fileName,
    baseName,
    imagePath,
    features
  } = caseData;

  // 使用新的imagePath字段
  const imageUrl = imagePath;

  const handleImageClick = () => {
    if (imageUrl) {
      setSelectedImage(imageUrl);
      setImageModalOpen(true);
    }
  };

  const closeImageModal = () => {
    setImageModalOpen(false);
    setSelectedImage(null);
  };

  // 注意：解析器数据现在由 ParserRow 组件内部处理，这里不再需要预先解构

  return (
    <div className="case-detail">
      <div className="case-detail-header">
        <h3>案例详情 - #{index}</h3>
        <div className="case-detail-filename">{fileName}</div>
      </div>

      {/* 案例信息布局 - 左右分栏 */}
      <div className="case-info-layout">
        {/* 左侧：案例信息和图片预览 */}
        <div className="case-info-left">
          <div className="case-info-block">
            <div className="case-info-header">
              <h4>案例信息</h4>
            </div>
            <div className="case-info-content">
              <div className="sequence-number">#{index}</div>
              <div className="file-info">
                <div className="file-name">{fileName}</div>
                <div className="base-name">基础名: {baseName}</div>
              </div>
            </div>
          </div>

          {/* 图片预览 */}
          <div className="image-preview-block">
            {imageUrl ? (
              <div className="image-preview-container">
                <img
                  src={imageUrl}
                  alt={fileName}
                  className="image-preview"
                  onClick={handleImageClick}
                  onLoad={() => logger.debug('图片加载成功:', imageUrl)}
                  onError={(e) => {
                    logger.error('图片加载失败:', imageUrl);
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'block';
                  }}
                />
                <div style={{ display: 'none', color: '#dc3545', fontSize: '12px', textAlign: 'center', padding: '20px' }}>
                  图片加载失败: {imageUrl}
                </div>
              </div>
            ) : (
              <div className="image-preview-container">
                <div style={{ color: '#666', fontSize: '12px', textAlign: 'center', padding: '20px' }}>
                  无图片
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 右侧：人工标注数据 */}
        <div className="case-info-right">
          {annotationData ? (
            <div className="annotation-block">
              <div className="annotation-header">
                <h4>人工标注数据</h4>
              </div>
              <div className="annotation-content">
                {/* 表格渲染结果 */}
                <div className="annotation-table-section">
                  <h5 style={{ margin: '0 0 10px 0', fontSize: '14px', color: '#333' }}>表格渲染结果</h5>
                  <TableRenderer
                    content={annotationData ? (() => {
                      try {
                        return JSON.parse(annotationData.table_content);
                      } catch (e) {
                        logger.error('解析标注数据失败:', e);
                        return null;
                      }
                    })() : null}
                    type="json_schema"
                    placeholder="无标注数据"
                  />
                </div>

                {/* Canvas渲染结果 */}
                <div className="annotation-canvas-section" style={{ marginTop: '15px' }}>
                  <h5 style={{ margin: '0 0 10px 0', fontSize: '14px', color: '#333' }}>Bbox渲染结果</h5>
                  <AnnotationCanvasRenderer
                    annotationData={annotationData ? (() => {
                      try {
                        return JSON.parse(annotationData.table_content);
                      } catch (e) {
                        logger.error('解析标注数据失败:', e);
                        return null;
                      }
                    })() : null}
                    placeholder="无bbox数据"
                  />
                </div>
              </div>
            </div>
          ) : (
            <div className="annotation-block">
              <div className="annotation-header">
                <h4>人工标注数据</h4>
              </div>
              <div className="annotation-content">
                <div style={{ color: '#666', fontSize: '14px', textAlign: 'center', padding: '40px' }}>
                  暂无人工标注数据
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 统计结果汇总块 */}
      {/* 指标汇总图表 */}
      <MetricsSummaryChart
        caseData={caseData}
        index={index}
        fileName={fileName}
        annotationData={annotationData}
      />

      {/* 准确率计算基准信息 */}
      <div className="baseline-control-block">
        <div className="baseline-info">
          {annotationData ? 
            '准确率计算基准: 人工标注数据' : 
            '无人工标注数据，无法计算准确率'
          }
        </div>
      </div>

      {/* 特征信息和指标信息并列块 */}
      {features && features.kdc && (
        <div className="features-metrics-container">
          {/* 特征信息块 */}
          <div className="features-summary-block">
            <div className="features-summary-header">
              <h4>特征信息</h4>
            </div>
            <div className="features-summary-content">
              <div className="feature-item">
                <div className="feature-label">KDC Bbox数量</div>
                <div className="feature-value">{features.kdc.bbox_count || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">平均X坐标</div>
                <div className="feature-value">{features.kdc.bbox_position_metrics?.avg_x || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">平均Y坐标</div>
                <div className="feature-value">{features.kdc.bbox_position_metrics?.avg_y || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">平均宽度</div>
                <div className="feature-value">{features.kdc.bbox_position_metrics?.avg_width || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">平均高度</div>
                <div className="feature-value">{features.kdc.bbox_position_metrics?.avg_height || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">Bbox密度</div>
                <div className="feature-value">{features.kdc.bbox_position_metrics?.bbox_density || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">文本框数量</div>
                <div className="feature-value">{features.kdc.bbox_types?.textbox || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">表格数量</div>
                <div className="feature-value">{features.kdc.bbox_types?.table || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">组件数量</div>
                <div className="feature-value">{features.kdc.bbox_types?.component || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">表格单元格数量</div>
                <div className="feature-value">{features.kdc.bbox_types?.table_cell || 0}</div>
              </div>
              <div className="feature-item">
                <div className="feature-label">检测到表格</div>
                <div className={`feature-value ${features.kdc.table_detection?.has_table ? 'feature-value-success' : 'feature-value-warning'}`}>
                  {features.kdc.table_detection?.has_table ? '是' : '否'}
                </div>
              </div>
              <div className="feature-item">
                <div className="feature-label">表格检测数量</div>
                <div className="feature-value">{features.kdc.table_detection?.table_count || 0}</div>
              </div>
            </div>

            {/* 文本框数量分布图表 */}
            {features.kdc.textbox_count_distribution && (
              <TextboxDistributionChart
                distributionData={features.kdc.textbox_count_distribution}
                className="textbox-distribution-feature"
              />
            )}
          </div>

          {/* 指标信息块 */}
          <div className="metrics-summary-block">
            <MetricsPanel caseData={caseData} />
          </div>
        </div>
      )}

      {/* 多路解析详情块 */}
      <div className="parsing-details-block">
        <div className="parsing-details-header">
          <h4>解析详情</h4>
          <div className="parsing-details-controls">
            <button
              className="toggle-original-text-btn"
              onClick={() => setShowOriginalText(!showOriginalText)}
              title={showOriginalText ? "隐藏原始文本列" : "显示原始文本列"}
            >
              <span className={`toggle-arrow ${showOriginalText ? 'expanded' : 'collapsed'}`}>
                ▶
              </span>
              {showOriginalText ? '隐藏原始文本' : '显示原始文本'}
            </button>
          </div>
        </div>
        <div style={{ overflowX: 'auto', width: '100%' }}>
          <div className={`case-detail-content ${!showOriginalText ? 'hide-original-text' : ''}`}>
            {/* 动态渲染解析器行 */}
            {getAvailableParserConfigs(caseData, enabledParsers).map((config, index) => (
              <ParserRow
                key={config.key}
                caseData={caseData}
                annotationData={annotationData}
                parserConfig={config}
                showOriginalText={showOriginalText}
                rowIndex={index + 1}
              />
            ))}

          </div>
        </div>
      </div>

      {imageModalOpen && (
        <ImageModal
          imageUrl={selectedImage}
          onClose={closeImageModal}
          title={fileName}
        />
      )}
    </div>
  );
});

export default CaseDetail;
