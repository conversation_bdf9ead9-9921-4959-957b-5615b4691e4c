.bbox-metrics {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
}

.bbox-metrics .metrics-header {
  margin-bottom: 10px;
}

.bbox-metrics .metrics-header h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 6px;
}

.bbox-metrics .metrics-summary {
  margin-bottom: 12px;
}

.bbox-metrics .metrics-row {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.bbox-metrics .metric-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  min-width: 120px;
}

.bbox-metrics .metric-item.primary {
  font-weight: 600;
}

.bbox-metrics .metric-label {
  color: #6c757d;
  white-space: nowrap;
}

.bbox-metrics .metric-value {
  color: #495057;
  font-weight: 500;
}

.bbox-metrics .metric-value.highlight {
  color: #007bff;
  font-weight: 600;
}

/* 详细匹配信息样式 */
.bbox-metrics .bbox-details {
  margin-top: 12px;
  border-top: 1px solid #dee2e6;
  padding-top: 10px;
}

.bbox-metrics .details-header h6 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 600;
  color: #495057;
}

.bbox-metrics .details-table {
  overflow-x: auto;
}

.bbox-metrics .details-table.scrollable {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.bbox-metrics .details-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 11px;
}

.bbox-metrics .details-table th,
.bbox-metrics .details-table td {
  padding: 4px 8px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.bbox-metrics .details-table th {
  background-color: #e9ecef;
  font-weight: 600;
  color: #495057;
}

.bbox-metrics .details-table td {
  color: #6c757d;
}

.bbox-metrics .match-status {
  font-weight: 600;
  font-size: 12px;
}

.bbox-metrics .match-status.match {
  color: #28a745;
}

.bbox-metrics .match-status.no-match {
  color: #dc3545;
}

.bbox-metrics .details-footer {
  margin-top: 6px;
  text-align: center;
  color: #6c757d;
  font-size: 10px;
}

/* 未匹配信息样式 */
.bbox-metrics .unmatched-info {
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px solid #dee2e6;
}

.bbox-metrics .unmatched-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.bbox-metrics .unmatched-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
}

.bbox-metrics .unmatched-label {
  color: #6c757d;
}

.bbox-metrics .unmatched-value {
  color: #dc3545;
  font-weight: 500;
}

/* 未匹配详情样式 */
.bbox-metrics .unmatched-details {
  margin-top: 10px;
}

.bbox-metrics .unmatched-section {
  margin-bottom: 15px;
}

.bbox-metrics .unmatched-section h7 {
  display: block;
  font-size: 11px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 6px 0;
}

.bbox-metrics .unmatched-section .details-table {
  margin-top: 5px;
}

.bbox-metrics .unmatched-section .details-table table {
  font-size: 10px;
}

.bbox-metrics .unmatched-section .details-table th,
.bbox-metrics .unmatched-section .details-table td {
  padding: 3px 6px;
}

.bbox-metrics .unmatched-section .details-table td {
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bbox-metrics .unmatched-section .details-table td[title] {
  cursor: help;
}

/* 说明文字样式 */
.bbox-metrics .bbox-explanation {
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px solid #dee2e6;
  color: #6c757d;
  font-size: 10px;
  line-height: 1.4;
}

/* 无数据状态样式 */
.bbox-metrics.no-data,
.bbox-metrics.no-result {
  background: #fff3cd;
  border-color: #ffeaa7;
}

.bbox-metrics .no-data-message,
.bbox-metrics .no-result-message {
  color: #856404;
  font-size: 12px;
  margin: 0;
  text-align: center;
  padding: 8px;
}

.bbox-metrics .metrics-content {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bbox-metrics .metrics-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .bbox-metrics .metric-item {
    min-width: auto;
  }
  
  .bbox-metrics .unmatched-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .bbox-metrics .details-table {
    font-size: 10px;
  }
  
  .bbox-metrics .details-table th,
  .bbox-metrics .details-table td {
    padding: 3px 6px;
  }
}
