import React, { useRef, useEffect, useState } from 'react';
import logger from '../utils/logger';

/**
 * 人工标注数据Canvas渲染器 - 基于bbox信息渲染不同类型的元素
 */
const AnnotationCanvasRenderer = ({ annotationData, placeholder = "无人工标注渲染结果" }) => {
  const canvasRef = useRef(null);
  const modalCanvasRef = useRef(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [annotationBlocks, setAnnotationBlocks] = useState([]);
  const [pageSize, setPageSize] = useState({ width: 1000, height: 1000 });

  // 元素类型颜色配置
  const ELEMENT_COLORS = {
    title: {
      background: 'rgba(255, 87, 34, ALPHA)',    // 橙红色 - 标题
      border: '#FF5722',
      name: '标题 (title)'
    },
    table: {
      background: 'rgba(76, 175, 80, ALPHA)',    // 绿色 - 表格
      border: '#4CAF50',
      name: '表格 (table)'
    },
    table_cell: {
      background: 'rgba(139, 195, 74, ALPHA)',   // 浅绿色 - 表格单元格
      border: '#8BC34A',
      name: '表格单元格 (table_cell)'
    },
    paragraph: {
      background: 'rgba(33, 150, 243, ALPHA)',   // 蓝色 - 段落
      border: '#2196F3',
      name: '段落 (paragraph)'
    },
    checkbox: {
      background: 'rgba(156, 39, 176, ALPHA)',   // 紫色 - 复选框
      border: '#9C27B0',
      name: '复选框 (checkbox)'
    },
    list: {
      background: 'rgba(255, 193, 7, ALPHA)',    // 黄色 - 列表
      border: '#FFC107',
      name: '列表 (list)'
    },
    text: {
      background: 'rgba(96, 125, 139, ALPHA)',   // 灰蓝色 - 文本
      border: '#607D8B',
      name: '文本 (text)'
    },
    unknown: {
      background: 'rgba(158, 158, 158, ALPHA)',  // 灰色 - 未知类型
      border: '#9E9E9E',
      name: '未知类型 (unknown)'
    }
  };

  // 获取元素类型对应的颜色
  const getElementColors = (elementType, alpha = 0.3) => {
    const colors = ELEMENT_COLORS[elementType] || ELEMENT_COLORS.unknown;
    return {
      background: colors.background.replace('ALPHA', alpha),
      border: colors.border,
      name: colors.name
    };
  };

  // 处理标注数据，提取bbox信息
  const processAnnotationData = (data) => {
    if (!data) {
      logger.warn('[AnnotationCanvasRenderer] 无标注数据');
      return { blocks: [], pageSize: { width: 1000, height: 1000 } };
    }

    logger.debug('[AnnotationCanvasRenderer] 处理标注数据:', data);

    const blocks = [];
    let maxWidth = 0;
    let maxHeight = 0;

    // 方法1: 从bbox_elements中获取bbox信息（[x1, y1, x2, y2]格式）
    if (data.bbox_elements && Array.isArray(data.bbox_elements)) {
      logger.debug('[AnnotationCanvasRenderer] 使用bbox_elements数据');

      data.bbox_elements.forEach((item, index) => {
        if (!item.bbox || !Array.isArray(item.bbox) || item.bbox.length < 4) {
          logger.warn(`[AnnotationCanvasRenderer] bbox_elements[${index}] 缺少有效的bbox数据`);
          return;
        }

        const [x1, y1, x2, y2] = item.bbox;

        // 更新页面尺寸
        maxWidth = Math.max(maxWidth, x2);
        maxHeight = Math.max(maxHeight, y2);

        blocks.push({
          index,
          elementType: item.type || 'unknown',
          x1, y1, x2, y2,
          width: x2 - x1,
          height: y2 - y1,
          text: item.text || item.content || '',
          source: 'bbox_elements'
        });
      });
    }

    // 方法2: 从elements中获取bbox信息（支持{x, y, width, height}和[x1, y1, x2, y2]格式）
    if (blocks.length === 0 && data.elements && Array.isArray(data.elements)) {
      logger.debug('[AnnotationCanvasRenderer] 使用elements数据');

      data.elements.forEach((element, elementIndex) => {
        // 处理表格类型的元素
        if (element.type === 'table' && element.rows) {
          // 表格整体的bbox
          if (element.bbox) {
            const bbox = element.bbox;
            let x1, y1, x2, y2;

            if (Array.isArray(bbox) && bbox.length >= 4) {
              // [x1, y1, x2, y2] 格式
              [x1, y1, x2, y2] = bbox;
            } else if (typeof bbox === 'object' && bbox.x !== undefined) {
              // {x, y, width, height} 格式
              x1 = bbox.x;
              y1 = bbox.y;
              x2 = bbox.x + bbox.width;
              y2 = bbox.y + bbox.height;
            } else {
              return; // 无效的bbox格式
            }

            maxWidth = Math.max(maxWidth, x2);
            maxHeight = Math.max(maxHeight, y2);

            blocks.push({
              index: elementIndex,
              elementType: 'table',
              x1, y1, x2, y2,
              width: x2 - x1,
              height: y2 - y1,
              text: element.caption || 'Table',
              source: 'table'
            });
          }
        }
        // 处理其他类型的元素
        else if (element.bbox) {
          const bbox = element.bbox;
          let x1, y1, x2, y2;

          if (Array.isArray(bbox) && bbox.length >= 4) {
            // [x1, y1, x2, y2] 格式
            [x1, y1, x2, y2] = bbox;
          } else if (typeof bbox === 'object' && bbox.x !== undefined) {
            // {x, y, width, height} 格式
            x1 = bbox.x;
            y1 = bbox.y;
            x2 = bbox.x + bbox.width;
            y2 = bbox.y + bbox.height;
          } else {
            return; // 无效的bbox格式
          }

          maxWidth = Math.max(maxWidth, x2);
          maxHeight = Math.max(maxHeight, y2);

          blocks.push({
            index: elementIndex,
            elementType: element.type || 'unknown',
            x1, y1, x2, y2,
            width: x2 - x1,
            height: y2 - y1,
            text: element.content || element.caption || '',
            source: 'element'
          });
        }
      });
    }

    // 如果没有找到任何bbox信息，返回空结果
    if (blocks.length === 0) {
      logger.warn('[AnnotationCanvasRenderer] 未找到bbox信息');
      return { blocks: [], pageSize: { width: 1000, height: 1000 } };
    }

    // 确保页面尺寸至少有最小值
    maxWidth = Math.max(maxWidth, 100);
    maxHeight = Math.max(maxHeight, 100);

    logger.debug(`[AnnotationCanvasRenderer] 处理完成，共 ${blocks.length} 个块，页面尺寸: ${maxWidth} × ${maxHeight}`);

    return { blocks, pageSize: { width: maxWidth, height: maxHeight } };
  };

  // 处理标注数据
  useEffect(() => {
    setIsLoading(true);
    setError(null);

    try {
      const result = processAnnotationData(annotationData);
      setAnnotationBlocks(result.blocks);
      setPageSize(result.pageSize);
      setIsLoading(false);
    } catch (err) {
      logger.error('[AnnotationCanvasRenderer] 处理标注数据失败:', err);
      setError(err.message);
      setIsLoading(false);
    }
  }, [annotationData]);

  // 绘制Canvas
  const drawCanvas = (canvas, scaleFactor) => {
    if (!canvas || !annotationBlocks.length) return;

    const ctx = canvas.getContext('2d');

    // 绘制背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    logger.debug(`[AnnotationCanvasRenderer] 绘制 ${annotationBlocks.length} 个标注块，缩放因子: ${scaleFactor}`);

    // 绘制标注块
    annotationBlocks.forEach((block, index) => {
      const x1 = Math.floor(block.x1 * scaleFactor);
      const y1 = Math.floor(block.y1 * scaleFactor);
      const x2 = Math.floor(block.x2 * scaleFactor);
      const y2 = Math.floor(block.y2 * scaleFactor);
      const width = x2 - x1;
      const height = y2 - y1;

      // 跳过无效的块
      if (width <= 0 || height <= 0) {
        logger.warn(`[AnnotationCanvasRenderer] 块 ${index} 尺寸无效，跳过`);
        return;
      }

      // 获取元素类型对应的颜色
      const colors = getElementColors(block.elementType, 0.3);

      // 绘制背景
      ctx.fillStyle = colors.background;
      ctx.fillRect(x1, y1, width, height);

      // 绘制边框
      ctx.strokeStyle = colors.border;
      ctx.lineWidth = 2;
      ctx.strokeRect(x1, y1, width, height);

      // 绘制类型标识
      if (width > 30 && height > 20) {
        ctx.fillStyle = colors.border;
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';
        ctx.textBaseline = 'bottom';
        
        const label = `${block.elementType}#${block.index}`;
        const labelX = x1 + 2;
        const labelY = y2 - 2;
        
        // 绘制文本背景
        const textMetrics = ctx.measureText(label);
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.fillRect(labelX - 1, labelY - 12, textMetrics.width + 2, 14);
        
        // 绘制文本
        ctx.fillStyle = colors.border;
        ctx.fillText(label, labelX, labelY);
      }
    });
  };

  // 渲染小图
  useEffect(() => {
    if (annotationBlocks.length > 0 && canvasRef.current) {
      const canvas = canvasRef.current;
      
      // 计算缩放比例
      const maxDisplayWidth = 600;
      const maxDisplayHeight = 400;
      const scaleX = maxDisplayWidth / pageSize.width;
      const scaleY = maxDisplayHeight / pageSize.height;
      const finalScale = Math.min(scaleX, scaleY, 1);

      const displayWidth = Math.floor(pageSize.width * finalScale);
      const displayHeight = Math.floor(pageSize.height * finalScale);

      canvas.width = displayWidth;
      canvas.height = displayHeight;
      canvas.style.width = `${displayWidth}px`;
      canvas.style.height = `${displayHeight}px`;

      drawCanvas(canvas, finalScale);
    }
  }, [annotationBlocks, pageSize]);

  // 打开大图模态窗口
  const openLargeCanvas = () => {
    setShowModal(true);
  };

  // 关闭大图模态窗口
  const closeLargeCanvas = () => {
    setShowModal(false);
  };

  // 渲染大图
  useEffect(() => {
    if (showModal && annotationBlocks.length > 0 && modalCanvasRef.current) {
      const canvas = modalCanvasRef.current;
      
      // 计算适合模态窗口的缩放比例
      const maxModalWidth = window.innerWidth * 0.8;
      const maxModalHeight = window.innerHeight * 0.7;
      const scaleX = maxModalWidth / pageSize.width;
      const scaleY = maxModalHeight / pageSize.height;
      const finalScale = Math.min(scaleX, scaleY, 1);

      const displayWidth = Math.floor(pageSize.width * finalScale);
      const displayHeight = Math.floor(pageSize.height * finalScale);

      canvas.width = displayWidth;
      canvas.height = displayHeight;
      canvas.style.width = `${displayWidth}px`;
      canvas.style.height = `${displayHeight}px`;

      drawCanvas(canvas, finalScale);
    }
  }, [showModal, annotationBlocks, pageSize]);

  if (isLoading) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <div>正在处理标注数据...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>
        <div>处理标注数据失败: {error}</div>
      </div>
    );
  }

  if (annotationBlocks.length === 0) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>
        <div>{placeholder}</div>
      </div>
    );
  }

  return (
    <div className="annotation-canvas-container">
      <div style={{ border: '1px solid #ddd', borderRadius: '4px', background: '#f8f9fa', padding: '10px' }}>
        {/* 颜色图例 - 只显示当前案例中存在的类型 */}
        <div style={{ marginBottom: '10px', padding: '8px', background: '#ffffff', borderRadius: '4px', border: '1px solid #e0e0e0' }}>
          <div style={{ fontSize: '12px', fontWeight: 'bold', marginBottom: '5px', color: '#333' }}>类型图例：</div>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', fontSize: '10px' }}>
            {(() => {
              // 获取当前案例中存在的elementType
              const existingTypes = [...new Set(annotationBlocks.map(block => block.elementType))];
              return existingTypes.map(elementType => {
                const colors = ELEMENT_COLORS[elementType] || ELEMENT_COLORS.unknown;
                return (
                  <div key={elementType} style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    <div style={{
                      width: '12px',
                      height: '12px',
                      backgroundColor: colors.background.replace('ALPHA', '0.6'),
                      border: `1px solid ${colors.border}`,
                      borderRadius: '2px'
                    }}></div>
                    <span>{colors.name}</span>
                  </div>
                );
              });
            })()}
          </div>
        </div>
        
        <canvas
          ref={canvasRef}
          style={{
            border: '1px solid #ccc',
            background: 'white',
            display: 'block',
            margin: '0 auto',
            cursor: 'pointer',
            maxWidth: '100%',
            height: 'auto'
          }}
          onClick={openLargeCanvas}
        />
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#666', textAlign: 'center' }}>
          原始尺寸: {pageSize.width} × {pageSize.height} | 标注块数量: {annotationBlocks.length}
          <br />
          <span style={{ color: '#007bff', cursor: 'pointer' }} onClick={openLargeCanvas}>
            🔍 点击图片查看大图
          </span>
        </div>
      </div>

      {/* 大图模态窗口 */}
      {showModal && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}
          onClick={closeLargeCanvas}
        >
          <div
            style={{
              background: 'white',
              borderRadius: '8px',
              padding: '20px',
              maxWidth: '90%',
              maxHeight: '90%',
              overflow: 'auto',
              position: 'relative'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={closeLargeCanvas}
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                background: 'none',
                border: 'none',
                fontSize: '24px',
                cursor: 'pointer',
                color: '#666'
              }}
            >
              ×
            </button>
            
            <h3 style={{ marginTop: 0, marginBottom: '15px' }}>人工标注数据 - 详细视图</h3>
            
            {/* 大图的颜色图例 */}
            <div style={{ marginBottom: '15px', padding: '10px', background: '#f8f9fa', borderRadius: '4px' }}>
              <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '8px' }}>类型图例：</div>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '12px', fontSize: '12px' }}>
                {(() => {
                  const existingTypes = [...new Set(annotationBlocks.map(block => block.elementType))];
                  return existingTypes.map(elementType => {
                    const colors = ELEMENT_COLORS[elementType] || ELEMENT_COLORS.unknown;
                    return (
                      <div key={elementType} style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                        <div style={{
                          width: '16px',
                          height: '16px',
                          backgroundColor: colors.background.replace('ALPHA', '0.6'),
                          border: `2px solid ${colors.border}`,
                          borderRadius: '3px'
                        }}></div>
                        <span>{colors.name}</span>
                      </div>
                    );
                  });
                })()}
              </div>
            </div>
            
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'auto',
                background: '#f8f9fa',
                borderRadius: '4px'
              }}
            >
              <canvas
                ref={modalCanvasRef}
                style={{
                  border: '1px solid #ccc',
                  background: 'white',
                  maxWidth: '95%',
                  maxHeight: '95%',
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                }}
              />
            </div>
            <div style={{ textAlign: 'center', padding: '10px 0', color: '#666', fontSize: '12px' }}>
              💡 提示：不同颜色代表不同的元素类型，右下角显示类型标识 (#序号)
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnnotationCanvasRenderer;
