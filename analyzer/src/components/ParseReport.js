import React, { useMemo } from 'react';
import logger from '../utils/logger';
import {
  calculateMetricsForCase,
  getCompleteTableComparison,
  parseTableContent,
  getParserKey
} from '../utils/dataProcessor';
import ParseStatus from './ParseStatus';
import AccuracyMetrics from './AccuracyMetrics';
import TedsMetrics from './TedsMetrics';
import BboxMetrics from './BboxMetrics';
import './ParseReport.css';

/**
 * 解析报告组件 - 重构版本
 * 使用子组件显示：解析状态、准确率指标、TEDS指标、Bbox指标
 */
const ParseReport = ({
  caseData,
  annotationData,
  parserName,
  actualText,
  isTimeout = false,
  processingTime = null
}) => {
  // 判断是否为版式识别算法
  const isLayoutParser = useMemo(() => {
    const layoutParsers = ['Mineru Layout', 'Mineru（VLM）', 'KDC KDC'];
    const isLayout = layoutParsers.includes(parserName);
    logger.debug(`[ParseReport] ${parserName}: 是否为版式识别算法: ${isLayout}`);
    return isLayout;
  }, [parserName]);

  // 获取解析器类型（用于bbox提取）
  const getParserType = (parserName) => {
    const typeMap = {
      'Mineru Layout': 'mineru_layout',
      'Mineru（VLM）': 'mineru_vlm',
      'KDC KDC': 'kdc'
    };
    return typeMap[parserName] || 'unknown';
  };
  const metricsData = useMemo(() => {
    if (!caseData) return null;

    const metrics = calculateMetricsForCase(caseData, annotationData);
    logger.debug('[ParseReport] metricsData:', metrics);
    if (!metrics) return null;

    // 获取当前解析器的指标
    const parserKey = getParserKey(parserName);
    if (!parserKey) return null;

    return {
      hasTable: metrics.hasTableResults?.[parserKey] || false,
      accuracy: metrics.accuracies?.[parserKey] || null,
      teds: metrics.tedsScores?.[parserKey] || null,
      tedsDetail: metrics.tedsDetails?.[parserKey] || null,
      hasAnnotationData: metrics.hasAnnotationData,
      comparisonData: metrics.comparisonData?.[parserKey] || null
    };
  }, [caseData, annotationData, parserName]);

  const comparisonResult = useMemo(() => {
    if (!metricsData?.hasAnnotationData) {
      logger.info(`[ParseReport] ${parserName}: 没有标注数据`);
      return null;
    }

    // 直接使用已经提取的当前解析器的比对数据
    if (metricsData.comparisonData) {
      logger.info(`[ParseReport] ${parserName}: 使用预计算的比对数据`);
      return metricsData.comparisonData;
    }

    logger.warn(`[ParseReport] ${parserName}: 没有比对数据 - 该解析器的输出格式不支持单元格比对`);
    return null;
  }, [caseData, annotationData, parserName, metricsData]);

  // 处理没有标注数据的情况
  if (!metricsData?.hasAnnotationData) {
    return (
      <div className="parse-report">
        <ParseStatus
          parserName={parserName}
          result={{ hasTable: metricsData?.hasTable }}
          isSuccess={metricsData?.hasTable}
        />

        {isLayoutParser ? (
          <BboxMetrics
            parserName={parserName}
            parserData={caseData ? caseData[getParserKey(parserName)] : null}
            parserType={getParserType(parserName)}
            annotationData={null}
            hasAnnotationData={false}
          />
        ) : (
          <div className="no-annotation-data">
            💡 无标注数据，无法计算准确率和TEDS指标
          </div>
        )}
      </div>
    );
  }

  // 处理超时情况
  if (isTimeout) {
    return (
      <div className="parse-report">
        <ParseStatus 
          parserName={parserName}
          result={{ timeout: true, processingTime }}
          isSuccess={false}
        />
      </div>
    );
  }

  return (
    <div className="parse-report">
      {/* 解析状态组件 */}
      <ParseStatus
        parserName={parserName}
        result={{ hasTable: metricsData.hasTable, content: actualText }}
        isSuccess={metricsData.hasTable}
      />

      {/* 版式识别算法显示Bbox指标，其他算法显示准确率和TEDS指标 */}
      {isLayoutParser ? (
        /* Bbox指标组件 - 仅用于版式识别算法 */
        <BboxMetrics
          parserName={parserName}
          parserData={caseData ? caseData[getParserKey(parserName)] : null}
          parserType={getParserType(parserName)}
          annotationData={annotationData ? (() => {
            try {
              return JSON.parse(annotationData.table_content);
            } catch (e) {
              logger.error('解析标注数据失败:', e);
              return null;
            }
          })() : null}
          hasAnnotationData={metricsData.hasAnnotationData}
        />
      ) : (
        <>
          {/* 准确率指标组件 - 用于OCR算法 */}
          <AccuracyMetrics
            parserName={parserName}
            comparisonResult={comparisonResult}
            hasAnnotationData={metricsData.hasAnnotationData}
          />

          {/* TEDS指标组件 - 用于OCR算法 */}
          <TedsMetrics
            parserName={parserName}
            tedsResult={metricsData.tedsDetail}
            hasAnnotationData={metricsData.hasAnnotationData}
          />
        </>
      )}
    </div>
  );
};

export default ParseReport;
