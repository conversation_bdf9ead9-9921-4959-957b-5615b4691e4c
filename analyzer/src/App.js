import React, { useState, useCallback, useMemo, useEffect } from 'react';
import logger from './utils/logger';
import { calculateMetricsForCase } from './utils/dataProcessor';
import { getDatasetAnnotations } from './services/api';
import { ParserProvider } from './contexts/ParserContext';
import DatasetSelector from './components/DatasetSelector';
import CaseList from './components/CaseList';
import CaseDetail from './components/CaseDetail';
import AnnotationPanel from './components/annotation/AnnotationPanel';

import './App.css';

// 在组件内部设置logger级别，避免模块级别的执行问题

// 添加全局调试函数
window.debugAccuracy = function(caseIndex = 0) {
  console.log('=== 调试准确率计算 ===');

  // 获取当前选中的数据集和案例
  const dataset = window.currentDataset || 'kingsoft';
  let casesData = null;

  // 先获取所有标注数据
  fetch(`http://localhost:8000/api/annotations?dataset_name=${dataset}&limit=1000`)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then(annotations => {
      console.log(`获取到 ${annotations.length} 条标注数据`);

      // 然后获取解析结果
      return fetch(`http://localhost:8000/api/datasets/${dataset}/parse_results`)
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          return response.json();
        })
        .then(parseResults => {
          casesData = parseResults;
          const caseData = parseResults[caseIndex];
          console.log('解析结果数据:', caseData);

          // 获取当前案例的文件名
          const fileName = parseResults[caseIndex]?.fileName || parseResults[caseIndex]?.metadata?.fileName;
          if (!fileName) {
            throw new Error('无法获取案例文件名');
          }
          console.log('当前案例文件名:', fileName);

          // 从已获取的标注数据中查找匹配的标注
          const annotation = annotations.find(a => {
            // 直接匹配
            if (a.image_filename === fileName) {
              return true;
            }

            // 去掉扩展名匹配
            const baseFileName = fileName.replace(/\.[^/.]+$/, '');
            const baseAnnotationName = a.image_filename.replace(/\.[^/.]+$/, '');
            return baseFileName === baseAnnotationName;
          });

          console.log('标注数据:', annotation);

          if (window.calculateMetricsForCase) {
            const result = window.calculateMetricsForCase(caseData, annotation);
            console.log('计算结果:', result);
          } else {
            console.log('calculateMetricsForCase函数不存在');
          }
        });
    })
    .catch(error => {
      console.error('调试失败:', error);
    });
};

// 暴露调试函数到全局
window.calculateMetricsForCase = calculateMetricsForCase;

console.log('💡 使用 debugAccuracy() 来调试准确率计算');

function App() {
  // 设置logger级别
  useEffect(() => {
    logger.setLevel('INFO');
  }, []);

  const [selectedDataset, setSelectedDataset] = useState('kingsoft');
  const [selectedCase, setSelectedCase] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('analysis'); // analysis | annotation
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [annotationsData, setAnnotationsData] = useState([]); // 数据集的所有标注数据
  const [annotationsLoading, setAnnotationsLoading] = useState(false);

  const handleDatasetChange = useCallback((dataset) => {
    setSelectedDataset(dataset);
    setSelectedCase(null);
    setAnnotationsData([]); // 清空之前的标注数据
  }, []);

  // 获取数据集的标注数据
  useEffect(() => {
    const loadAnnotations = async () => {
      if (!selectedDataset) {
        setAnnotationsData([]);
        return;
      }

      setAnnotationsLoading(true);
      try {
        const datasetName = selectedDataset.name || selectedDataset;
        const annotations = await getDatasetAnnotations(datasetName);
        setAnnotationsData(annotations || []);
        logger.info(`[App] 加载数据集 ${datasetName} 的标注数据:`, {
          count: annotations?.length || 0
        });
      } catch (error) {
        const datasetName = selectedDataset.name || selectedDataset;
        logger.error(`[App] 加载数据集 ${datasetName} 标注数据失败:`, error);
        setAnnotationsData([]);
      } finally {
        setAnnotationsLoading(false);
      }
    };

    loadAnnotations();
  }, [selectedDataset]);

  // 根据文件名查找对应的标注数据
  const findAnnotationForCase = useCallback((fileName) => {
    if (!fileName || !annotationsData.length) {
      return null;
    }

    // 查找匹配的标注 - 支持不同扩展名的文件匹配
    const matchingAnnotation = annotationsData.find(ann => {
      // 直接匹配
      if (ann.image_filename === fileName) {
        return true;
      }

      // 去掉扩展名匹配
      const baseFileName = fileName.replace(/\.[^/.]+$/, '');
      const baseAnnotationName = ann.image_filename.replace(/\.[^/.]+$/, '');
      return baseFileName === baseAnnotationName;
    });

    return matchingAnnotation || null;
  }, [annotationsData]);

  const handleCaseSelect = useCallback((caseData) => {
    setSelectedCase(caseData);
  }, []);

  // 使用 useMemo 优化渲染性能
  const datasetSelector = useMemo(() => (
    <DatasetSelector
      selectedDataset={selectedDataset}
      onDatasetChange={handleDatasetChange}
      disabled={loading}
    />
  ), [selectedDataset, handleDatasetChange, loading]);

  const caseList = useMemo(() => (
    <CaseList
      selectedDataset={selectedDataset}
      selectedCase={selectedCase}
      onCaseSelect={handleCaseSelect}
    />
  ), [selectedDataset, selectedCase, handleCaseSelect]);

  const caseDetail = useMemo(() => (
    <CaseDetail
      caseData={selectedCase}
      dataset={selectedDataset}
      annotationData={selectedCase ? findAnnotationForCase(selectedCase.fileName) : null}
      annotationsLoading={annotationsLoading}
    />
  ), [selectedCase, selectedDataset, findAnnotationForCase, annotationsLoading]);

  const annotationPanel = useMemo(() => (
    <AnnotationPanel
      selectedCase={selectedCase}
      selectedDataset={selectedDataset}
      annotationData={selectedCase ? findAnnotationForCase(selectedCase.fileName) : null}
      annotationsLoading={annotationsLoading}
      onAnnotationChange={() => {
        // 标注变化时的回调，可以用来刷新数据
        logger.info('标注数据已更新');
      }}
    />
  ), [selectedCase, selectedDataset, findAnnotationForCase, annotationsLoading]);

  return (
    <ParserProvider>
      <div className="app">
        <header className="app-header">
          <div className="container">
            <h1 className="app-title">算法评测平台</h1>
            <p className="app-subtitle">基于React的表格解析结果分析工具</p>
          </div>
        </header>

      <main className="app-main">
        <div className="container">
          {/* 数据集选择器 */}
          <section className="app-section">
            {datasetSelector}
          </section>

          {/* 标签切换 */}
          <section className="app-section">
            <div className="tab-navigation">
              <button
                className={`tab-button ${activeTab === 'analysis' ? 'active' : ''}`}
                onClick={() => setActiveTab('analysis')}
              >
                解析分析
              </button>
              <button
                className={`tab-button ${activeTab === 'annotation' ? 'active' : ''}`}
                onClick={() => setActiveTab('annotation')}
              >
                人工标注
              </button>
            </div>
          </section>

          {/* 主要内容区域 */}
          <div className="app-content">
            {/* 左侧：案例列表 */}
            <aside className={`app-sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}>
              <div className="sidebar-toggle">
                <button
                  className="sidebar-toggle-btn"
                  onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                  title={sidebarCollapsed ? '展开案例列表' : '收缩案例列表'}
                >
                  {sidebarCollapsed ? '▶' : '◀'}
                </button>
              </div>
              {!sidebarCollapsed && caseList}
            </aside>

            {/* 右侧：内容区域 */}
            <section className="app-detail">
              {activeTab === 'analysis' ? caseDetail : annotationPanel}
            </section>
          </div>
        </div>
      </main>

        <footer className="app-footer">
          <div className="container">
            <p>&copy; 2025 TableRAG Analyzer. 基于React构建的现代化分析工具。</p>
          </div>
        </footer>
      </div>
    </ParserProvider>
  );
}

export default App;
